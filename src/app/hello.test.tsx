import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import HelloScreen from './hello';
import { Alert } from 'react-native';

jest.spyOn(Alert, 'alert');

let mockSetOptions: jest.Mock;

jest.mock('expo-router', () => {
	mockSetOptions = jest.fn(); // ✅
	return {
		useNavigation: () => ({
			setOptions: mockSetOptions,
		}),
	};
});

describe('<HelloScreen />', () => {
	it('renders the text "Hello Navigation"', () => {
		const { getByText } = render(<HelloScreen />);
		expect(getByText('Hello Navigation')).toBeTruthy();
	});

	it('calls Alert.alert when the header button is pressed', () => {
		render(<HelloScreen />);

		const headerConfig = mockSetOptions.mock.calls[0][0];
		const HeaderRightButton = headerConfig.headerRight;

		const { getByText } = render(<HeaderRightButton />);
		fireEvent.press(getByText('Click Me'));

		expect(Alert.alert).toHaveBeenCalledWith('Button pressed!');
	});
});
