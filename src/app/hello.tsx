import { StyleSheet, Al<PERSON>, Button } from 'react-native';
import { useNavigation } from 'expo-router';
import { useLayoutEffect } from 'react';

import { useDispatch, useSelector } from 'react-redux';
import type { RootState, AppDispatch } from '@/app/store';
import { setMessage, resetMessage } from '@/features/hello/helloSlice';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import CustomButton from '@/components/ui/CustomButton';

export default function HelloScreen() {
	const navigation = useNavigation();
	const dispatch = useDispatch<AppDispatch>();
	const message = useSelector((state: RootState) => state.hello.message);

	useLayoutEffect(() => {
		navigation.setOptions({
			headerRight: () => (
				<Button onPress={() => Alert.alert('Button pressed!')} title='Click Me' />
			),
		});
	}, [navigation]);

	return (
		<ThemedView style={styles.container}>
			<ThemedText type='title'>{message}</ThemedText>
			<CustomButton variant='primary' onPress={() => dispatch(setMessage('🎈Happy Day🎈'))}>
				Send Message
			</CustomButton>
			<CustomButton variant='second' onPress={() => dispatch(resetMessage())}>
				Reset
			</CustomButton>
		</ThemedView>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		paddingHorizontal: 24,
		gap: 10,
	},
});
