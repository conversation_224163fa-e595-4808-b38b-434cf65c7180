import React, { useEffect, useState } from 'react';
import { View, Text, Button, StyleSheet, ActivityIndicator } from 'react-native';
import { OktaAuth } from '@okta/okta-react-native';

/**
 * Test component to verify Okta SDK functionality with React 19
 */
const OktaTest: React.FC = () => {
  const [status, setStatus] = useState<string>('Initializing...');
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Okta configuration (replace with actual values in production)
  const oktaConfig = {
    clientId: 'YOUR_CLIENT_ID',
    redirectUri: 'com.learningcoach:/callback',
    endSessionRedirectUri: 'com.learningcoach:/logout',
    discoveryUri: 'https://your-okta-domain/oauth2/default'
  };

  // Initialize Okta when component mounts
  useEffect(() => {
    const initializeOkta = async () => {
      try {
        setIsLoading(true);
        setStatus('Configuring Okta SDK...');
        
        // Configure the Okta SDK
        await OktaAuth.createConfig({
          clientId: oktaConfig.clientId,
          redirectUri: oktaConfig.redirectUri,
          endSessionRedirectUri: oktaConfig.endSessionRedirectUri,
          discoveryUri: oktaConfig.discoveryUri,
          scopes: ['openid', 'profile', 'email', 'offline_access'],
          requireHardwareBackedKeyStore: false // For development only
        });
        
        setStatus('Checking authentication status...');
        const authState = await OktaAuth.isAuthenticated();
        setIsAuthenticated(authState);
        
        if (authState) {
          setStatus('Getting access token...');
          const token = await OktaAuth.getAccessToken();
          setAccessToken(token);
          setStatus('Authentication verified.');
        } else {
          setStatus('Not authenticated.');
        }
        
        setIsLoading(false);
      } catch (err: any) {
        setError(err.message || 'Failed to initialize Okta');
        setStatus('Error initializing.');
        setIsLoading(false);
      }
    };

    initializeOkta();
  }, []);

  // Handle login button press
  const handleLogin = async () => {
    try {
      setIsLoading(true);
      setStatus('Signing in...');
      
      // Start the Okta sign-in flow
      await OktaAuth.signIn();
      
      // Check if authentication succeeded
      const authState = await OktaAuth.isAuthenticated();
      setIsAuthenticated(authState);
      
      if (authState) {
        const token = await OktaAuth.getAccessToken();
        setAccessToken(token);
        setStatus('Signed in successfully.');
      } else {
        setStatus('Sign-in failed.');
      }
      
      setIsLoading(false);
    } catch (err: any) {
      setError(err.message || 'Failed to sign in');
      setStatus('Error signing in.');
      setIsLoading(false);
    }
  };

  // Handle logout button press
  const handleLogout = async () => {
    try {
      setIsLoading(true);
      setStatus('Signing out...');
      
      // Revoke tokens and sign out
      await OktaAuth.signOut();
      
      setIsAuthenticated(false);
      setAccessToken(null);
      setStatus('Signed out successfully.');
      setIsLoading(false);
    } catch (err: any) {
      setError(err.message || 'Failed to sign out');
      setStatus('Error signing out.');
      setIsLoading(false);
    }
  };

  // Handle token refresh
  const handleRefreshToken = async () => {
    try {
      setIsLoading(true);
      setStatus('Refreshing token...');
      
      // Refresh the tokens
      await OktaAuth.refreshTokens();
      
      // Get the new access token
      const token = await OktaAuth.getAccessToken();
      setAccessToken(token);
      setStatus('Token refreshed successfully.');
      setIsLoading(false);
    } catch (err: any) {
      setError(err.message || 'Failed to refresh token');
      setStatus('Error refreshing token.');
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Okta SDK Test</Text>
      
      {isLoading && (
        <ActivityIndicator size="large" color="#0000ff" />
      )}
      
      <Text style={styles.status}>Status: {status}</Text>
      
      {isAuthenticated !== null && (
        <Text style={styles.info}>
          Authenticated: {isAuthenticated ? 'Yes' : 'No'}
        </Text>
      )}
      
      {accessToken && (
        <View style={styles.tokenContainer}>
          <Text style={styles.label}>Access Token:</Text>
          <Text style={styles.tokenText} numberOfLines={2} ellipsizeMode="tail">
            {accessToken}
          </Text>
        </View>
      )}
      
      {error && (
        <Text style={styles.error}>Error: {error}</Text>
      )}
      
      <View style={styles.buttonContainer}>
        {!isAuthenticated ? (
          <Button
            title="Sign In"
            onPress={handleLogin}
            disabled={isLoading}
          />
        ) : (
          <>
            <Button
              title="Refresh Token"
              onPress={handleRefreshToken}
              disabled={isLoading}
            />
            <View style={styles.buttonSpacer} />
            <Button
              title="Sign Out"
              onPress={handleLogout}
              disabled={isLoading}
              color="#ff3b30"
            />
          </>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  status: {
    fontSize: 16,
    marginVertical: 10,
  },
  info: {
    fontSize: 16,
    marginBottom: 10,
  },
  tokenContainer: {
    width: '100%',
    padding: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 5,
    marginVertical: 10,
  },
  label: {
    fontWeight: 'bold',
    marginBottom: 5,
  },
  tokenText: {
    fontSize: 12,
  },
  error: {
    color: 'red',
    marginVertical: 10,
  },
  buttonContainer: {
    flexDirection: 'row',
    marginTop: 20,
  },
  buttonSpacer: {
    width: 10,
  },
});

export default OktaTest;
