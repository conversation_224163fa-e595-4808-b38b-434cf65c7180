/**
 * Main configuration service for the Learning Coach Community app
 *
 * This service provides type-safe access to environment variables and configuration
 * with validation and fallback values.
 */

import Constants from 'expo-constants';
import { AppConfig, Environment, ConfigValidationError } from '../types/config';

/**
 * Get environment variable with fallback
 */
const getEnvVar = (key: string, fallback?: string): string => {
	const value = Constants.expoConfig?.extra?.[key] || process.env[key];
	return value || fallback || '';
};

/**
 * Get boolean environment variable
 */
const getBooleanEnvVar = (key: string, fallback: boolean = false): boolean => {
	const value = getEnvVar(key);
	if (!value) return fallback;
	return value.toLowerCase() === 'true' || value === '1';
};

/**
 * Get numeric environment variable
 */
const getNumericEnvVar = (key: string, fallback: number): number => {
	const value = getEnvVar(key);
	const parsed = parseInt(value, 10);
	return isNaN(parsed) ? fallback : parsed;
};

/**
 * Create application configuration
 */
const createConfig = (): AppConfig => {
	const environment = getEnvVar('NODE_ENV', 'development') as Environment;
	const isDevelopment = environment === 'development';
	const isProduction = environment === 'production';

	return {
		// Environment
		environment,
		version: Constants.expoConfig?.version || '1.0.0',
		buildNumber: getEnvVar('BUILD_NUMBER', '1'),

		// API Configuration
		api: {
			baseUrl: getEnvVar(
				'API_URL',
				isDevelopment ? 'http://localhost:3000/api' : 'https://api.learningcoach.com',
			),
			timeout: getNumericEnvVar('API_TIMEOUT', 10000),
			retryAttempts: getNumericEnvVar('API_RETRY_ATTEMPTS', 3),
		},

		// Authentication
		auth: {
			tokenStorageKey: 'auth_token',
			refreshTokenStorageKey: 'refresh_token',
			sessionTimeout: getNumericEnvVar('SESSION_TIMEOUT', 60), // 60 minutes
		},

		// Analytics
		analytics: {
			enabled: getBooleanEnvVar('ENABLE_ANALYTICS', isProduction),
			trackingId: getEnvVar('ANALYTICS_TRACKING_ID'),
			debugMode: getBooleanEnvVar('ANALYTICS_DEBUG', isDevelopment),
		},

		// Feature Flags
		features: {
			enablePushNotifications: getBooleanEnvVar('ENABLE_PUSH_NOTIFICATIONS', !isDevelopment),
			enableAnalytics: getBooleanEnvVar('ENABLE_ANALYTICS', isProduction),
			enableOfflineMode: getBooleanEnvVar('ENABLE_OFFLINE_MODE', true),
			enableBetaFeatures: getBooleanEnvVar('ENABLE_BETA_FEATURES', isDevelopment),
			enableDebugMenu: getBooleanEnvVar('ENABLE_DEBUG_MENU', isDevelopment),
		},

		// External Services
		services: {
			firebase: getEnvVar('FIREBASE_API_KEY')
				? {
						apiKey: getEnvVar('FIREBASE_API_KEY'),
						authDomain: getEnvVar('FIREBASE_AUTH_DOMAIN'),
						projectId: getEnvVar('FIREBASE_PROJECT_ID'),
					}
				: undefined,

			stripe: getEnvVar('STRIPE_PUBLISHABLE_KEY')
				? {
						publishableKey: getEnvVar('STRIPE_PUBLISHABLE_KEY'),
					}
				: undefined,

			sentry: getEnvVar('SENTRY_DSN')
				? {
						dsn: getEnvVar('SENTRY_DSN'),
					}
				: undefined,
		},
	};
};

/**
 * Validate configuration
 */
export const validateConfig = (config: AppConfig): ConfigValidationError[] => {
	const errors: ConfigValidationError[] = [];

	// Required fields
	if (!config.api.baseUrl) {
		errors.push({
			field: 'api.baseUrl',
			message: 'API base URL is required',
			required: true,
		});
	}

	// Production-specific validations
	if (config.environment === 'production') {
		if (config.features.enableAnalytics && !config.analytics.trackingId) {
			errors.push({
				field: 'analytics.trackingId',
				message:
					'Analytics tracking ID is required in production when analytics is enabled',
				required: true,
			});
		}

		if (!config.services.firebase?.apiKey) {
			errors.push({
				field: 'services.firebase.apiKey',
				message: 'Firebase API key is recommended for production',
				required: false,
			});
		}
	}

	// URL validations
	try {
		new URL(config.api.baseUrl);
	} catch {
		errors.push({
			field: 'api.baseUrl',
			message: 'API base URL must be a valid URL',
			required: true,
		});
	}

	return errors;
};

/**
 * Initialize and validate configuration
 */
const initializeConfig = (): AppConfig => {
	const config = createConfig();
	const errors = validateConfig(config);

	// Log configuration in development
	if (config.environment === 'development') {
		console.log('🔧 App Configuration:', {
			environment: config.environment,
			apiUrl: config.api.baseUrl,
			features: config.features,
		});
	}

	// Handle validation errors
	if (errors.length > 0) {
		const criticalErrors = errors.filter((e) => e.required);

		if (criticalErrors.length > 0) {
			console.error('❌ Critical configuration errors:', criticalErrors);
			throw new Error(
				`Configuration validation failed: ${criticalErrors.map((e) => e.message).join(', ')}`,
			);
		} else {
			console.warn('⚠️ Configuration warnings:', errors);
		}
	}

	return config;
};

// Export the initialized configuration
export const config = initializeConfig();

// Export individual config sections for convenience
export const apiConfig = config.api;
export const authConfig = config.auth;
export const analyticsConfig = config.analytics;
export const featureFlags = config.features;

// Utility functions
export const isDevelopment = config.environment === 'development';
export const isProduction = config.environment === 'production';
export const isStaging = config.environment === 'staging';
