import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert
} from 'react-native';
import { validateEmailField, validatePasswordField } from '@utils/validation';
import { AuthService } from '@services/authService';
import { AuthRepository } from '@repositories/authRepository';

/**
 * Login Screen Component
 * @returns React Component
 */
const Login = ({ navigation }: { navigation: any }) => {
  // Local state variables using useState
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isEmailValid, setIsEmailValid] = useState(false);
  const [isPasswordValid, setIsPasswordValid] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [generalError, setGeneralError] = useState('');
  
  // Create instance of AuthService
  const authService = new AuthService(new AuthRepository());
  
  // Validate email using utility function
  const validateEmail = (value = email) => {
    const result = validateEmailField(value);
    setEmailError(result.errorMessage);
    setIsEmailValid(result.isValid);
    return result.isValid;
  };
  
  // Validate password using utility function
  const validatePassword = (value = password) => {
    const result = validatePasswordField(value);
    setPasswordError(result.errorMessage);
    setIsPasswordValid(result.isValid);
    return result.isValid;
  };
  
  // Handle login submission
  const handleSubmit = async () => {
    // Reset errors
    setGeneralError('');
    
    // Validate inputs
    const emailIsValid = validateEmail();
    const passwordIsValid = validatePassword();
    
    if (!emailIsValid || !passwordIsValid) {
      return; // Stop submission if validation fails
    }
    
    try {
      setIsSubmitting(true);
      
      // Call auth service login method
      const result = await authService.login(email, password);
      
      if (result.success) {
        // Navigate to next screen on successful login
        navigation.navigate('Home');
      } else {
        setGeneralError('Login failed. Please try again.');
      }
    } catch (error) {
      // Handle specific error messages
      if (error instanceof Error) {
        if (error.message.includes('Invalid email') || 
            error.message.includes('Password')) {
          setGeneralError(error.message);
        } else if (error.message.includes('unavailable')) {
          setGeneralError('Service is temporarily unavailable. Please try again later.');
        } else {
          setGeneralError('Authentication failed. Please check your credentials and try again.');
        }
      } else {
        setGeneralError('An unexpected error occurred. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Determine if submit button should be enabled
  const isFormValid = () => {
    return isEmailValid && isPasswordValid;
  };
  
  // Effect to update validation status on component mount
  useEffect(() => {
    if (email) validateEmail();
    if (password) validatePassword();
  }, []);

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView 
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.formContainer}>
          <Text style={styles.title}>Welcome Back</Text>
          <Text style={styles.subtitle}>Sign in to continue</Text>
          
          {generalError ? (
            <View style={styles.errorContainer}>
              <Text style={styles.generalError}>{generalError}</Text>
            </View>
          ) : null}
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Email</Text>
            <TextInput
              style={[styles.input, emailError ? styles.inputError : null]}
              placeholder="Enter your email"
              placeholderTextColor="#aaa"
              value={email}
              onChangeText={(text) => {
                setEmail(text);
                validateEmail(text);
              }}
              onBlur={() => validateEmail()}
              autoCapitalize="none"
              keyboardType="email-address"
              testID="login-email-input"
              accessible={true}
              accessibilityLabel="Email input field"
            />
            {emailError ? <Text style={styles.errorText}>{emailError}</Text> : null}
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Password</Text>
            <TextInput
              style={[styles.input, passwordError ? styles.inputError : null]}
              placeholder="Enter your password"
              placeholderTextColor="#aaa"
              value={password}
              onChangeText={(text) => {
                setPassword(text);
                validatePassword(text);
              }}
              onBlur={() => validatePassword()}
              secureTextEntry
              testID="login-password-input"
              accessible={true}
              accessibilityLabel="Password input field"
            />
            {passwordError ? <Text style={styles.errorText}>{passwordError}</Text> : null}
          </View>
          
          <TouchableOpacity 
            style={[styles.button, !isFormValid() ? styles.buttonDisabled : null]} 
            onPress={handleSubmit}
            disabled={!isFormValid() || isSubmitting}
            testID="login-submit-button"
            accessible={true}
            accessibilityLabel="Sign in button"
          >
            {isSubmitting ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.buttonText}>Sign In</Text>
            )}
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.forgotPasswordContainer}
            onPress={() => navigation.navigate('ForgotPassword')}
          >
            <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
          </TouchableOpacity>
          
          <View style={styles.signupContainer}>
            <Text style={styles.signupText}>Don't have an account? </Text>
            <TouchableOpacity onPress={() => navigation.navigate('Register')}>
              <Text style={styles.signupLink}>Sign Up</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9f9f9',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  formContainer: {
    padding: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 32,
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    borderRadius: 8,
    padding: 12,
    marginBottom: 24,
  },
  generalError: {
    color: '#d32f2f',
    fontSize: 14,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    height: 50,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  inputError: {
    borderColor: '#d32f2f',
  },
  errorText: {
    color: '#d32f2f',
    fontSize: 14,
    marginTop: 4,
  },
  button: {
    height: 50,
    backgroundColor: '#007bff',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
  },
  buttonDisabled: {
    backgroundColor: '#cccccc',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  forgotPasswordContainer: {
    alignItems: 'center',
    marginTop: 20,
  },
  forgotPasswordText: {
    color: '#007bff',
    fontSize: 14,
  },
  signupContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 32,
  },
  signupText: {
    fontSize: 14,
    color: '#666',
  },
  signupLink: {
    fontSize: 14,
    color: '#007bff',
    fontWeight: '500',
  },
});

export default Login;