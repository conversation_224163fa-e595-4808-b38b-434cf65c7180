import { Platform } from 'react-native';
import {
  createConfig,
  signIn,
  signOut,
  isAuthenticated,
  getAccessToken,
  getIdToken,
  getUserInfo,
  refreshTokens,
  revokeAccessToken,
  revokeIdToken,
  revokeRefreshToken,
  EventEmitter,
  OktaState
} from '@okta/okta-react-native';
import axios from 'axios';

/**
 * OktaAuthRepository handles authentication through Okta OAuth 2.0 flow
 * for React Native applications
 */
export class OktaAuthRepository {
  private accessToken: string | null = null;
  private idToken: string | null = null;
  private refreshToken: string | null = null;
  private userInfo: any = null;
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * Initialize Okta configuration
   * @param config Configuration options for Okta
   */
  async configure(config: {
    clientId: string;
    redirectUri: string;
    endSessionRedirectUri: string;
    discoveryUri: string;
    scopes: string[];
    requireHardwareBackedKeyStore?: boolean;
  }): Promise<void> {
    try {
      await createConfig({
        clientId: config.clientId,
        redirectUri: config.redirectUri,
        endSessionRedirectUri: config.endSessionRedirectUri,
        discoveryUri: config.discoveryUri,
        scopes: config.scopes,
        requireHardwareBackedKeyStore: config.requireHardwareBackedKeyStore ?? false
      });
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  }

  /**
   * Check if user is authenticated with Okta
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      return await isAuthenticated();
    } catch (error) {
      throw error;
    }
  }

  /**
   * Initiate Okta sign-in flow
   * This will redirect to Okta login page and return after successful authentication
   */
  async login(): Promise<{
    success: boolean;
    error?: string;
    errorCode?: string;
  }> {
    try {
      const { resolve, reject } = await signIn();
      
      if (resolve) {
        // Store tokens for later use
        await this.storeTokens();
        return { 
          success: true 
        };
      } else {
        return {
          success: false,
          error: reject?.error || 'Unknown error',
          errorCode: reject?.errorCode
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Sign out the current user and clear tokens
   */
  async logout(): Promise<boolean> {
    try {
      await signOut();
      this.accessToken = null;
      this.idToken = null;
      this.refreshToken = null;
      this.userInfo = null;
      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get the current access token
   */
  getAccessToken(): string | null {
    return this.accessToken;
  }

  /**
   * Get the current ID token
   */
  getIdToken(): string | null {
    return this.idToken;
  }

  /**
   * Get the current refresh token
   */
  getRefreshToken(): string | null {
    return this.refreshToken;
  }

  /**
   * Get the current user info
   */
  getUserInfo(): any {
    return this.userInfo;
  }

  /**
   * Fetch and store all tokens (access, ID, refresh)
   * @private
   */
  private async storeTokens(): Promise<void> {
    try {
      // Get access token
      this.accessToken = await getAccessToken();
      
      // Get ID token
      this.idToken = await getIdToken();
      
      // Get user info
      this.userInfo = await getUserInfo();

      // Note: Refresh token is not directly accessible via the SDK
      // It's handled internally by the SDK for token refresh
      
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  }

  /**
   * Refresh the access and ID tokens using the refresh token
   */
  async refreshTokens(): Promise<boolean> {
    try {
      await refreshTokens();
      await this.storeTokens();
      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Revoke the access token
   */
  async revokeAccessToken(): Promise<boolean> {
    try {
      await revokeAccessToken();
      this.accessToken = null;
      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Revoke the ID token
   */
  async revokeIdToken(): Promise<boolean> {
    try {
      await revokeIdToken();
      this.idToken = null;
      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Revoke the refresh token
   */
  async revokeRefreshToken(): Promise<boolean> {
    try {
      await revokeRefreshToken();
      this.refreshToken = null;
      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Revoke all tokens
   */
  async revokeAllTokens(): Promise<boolean> {
    try {
      await this.revokeAccessToken();
      await this.revokeIdToken();
      await this.revokeRefreshToken();
      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get the roles assigned to a user by making an API call to the backend
   * @param userId User ID
   * @returns Array of role names
   */
  async getUserRoles(userId: string): Promise<string[]> {
    try {
      // Check if we have an access token
      if (!this.accessToken) {
        throw new Error('Not authenticated');
      }

      // Make API call to backend (assuming similar endpoint as original)
      const response = await axios.get(
        '/m/oauth2/com/get_user_roles',
        {
          params: { user_id: userId },
          headers: {
            'Authorization': `Bearer ${this.accessToken}`
          }
        }
      );
      return response.data.roles || [];
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get the permissions assigned to a user by making an API call to the backend
   * @param userId User ID
   * @returns Array of permission names
   */
  async getUserPermissions(userId: string): Promise<string[]> {
    try {
      // Check if we have an access token
      if (!this.accessToken) {
        throw new Error('Not authenticated');
      }

      // Make API call to backend (assuming similar endpoint as original)
      const response = await axios.get(
        '/m/oauth2/com/get_user_permissions',
        {
          params: { user_id: userId },
          headers: {
            'Authorization': `Bearer ${this.accessToken}`
          }
        }
      );
      return response.data.permissions || [];
    } catch (error) {
      throw error;
    }
  }

  /**
   * Add event listener for Okta state changes
   * @param event Event name
   * @param callback Callback function
   */
  addEventListener(event: string, callback: (data: OktaState) => void): void {
    this.eventEmitter.addListener(event, callback);
  }

  /**
   * Remove event listener for Okta state changes
   * @param event Event name
   * @param callback Callback function
   */
  removeEventListener(event: string, callback: (data: OktaState) => void): void {
    this.eventEmitter.removeListener(event, callback);
  }
}
