import axios from 'axios';

/**
 * Interface for login attempt check response
 */
interface LoginAttemptsCheck {
  remainingAttempts: number;
  lockedOut: boolean;
}

/**
 * AuthRepository handles all API routes for authentication and account management
 * as described in the UNA public API documentation
 */
export class AuthRepository {
    private accessToken: string | null = null;
    private refreshToken: string | null = null;
    private userInfo: any = null;
    private sessionId: string | null = null;

    /**
     * Get the current access token
     */
    getAccessToken(): string | null {
        return this.accessToken;
    }

    /**
     * Get the current refresh token
     */
    getRefreshToken(): string | null {
        return this.refreshToken;
    }

    /**
     * Get the current user info
     */
    getUserInfo(): any {
        return this.userInfo;
    }
    
    /**
     * Get the current session ID
     */
    getSessionId(): string | null {
        return this.sessionId;
    }

    /**
     * Test API connection using the test endpoint
     */
    async testApiConnection() {
        try {
            const response = await axios.get('/m/oauth2/com/test');
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Get page with cells and blocks as array
     * @param uri Page URI
     */
    async getPage(uri: string) {
        try {
            const response = await axios.get('/m/oauth2/com/get_page', { 
                params: { uri } 
            });
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Send email with reset password code
     * @param email Email address
     */
    async resetPasswordSendRequest(email: string) {
        try {
            const response = await axios.post(
                '/m/oauth2/com/reset_password_send_request',
                { email },
                { headers: { 'Content-Type': 'application/json' } }
            );
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Check validation code for password reset
     * @param email Email address
     * @param code Validation code
     */
    async resetPasswordCheckCode(email: string, code: string) {
        try {
            const response = await axios.post(
                '/m/oauth2/com/reset_password_check_code',
                { email, code },
                { headers: { 'Content-Type': 'application/json' } }
            );
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Create a new account
     * @param userData User data containing name, email, and password
     */
    async createAccount(userData: { name: string, email: string, password: string }) {
        try {
            const response = await axios.post(
                '/m/oauth2/com/create_account',
                userData,
                { headers: { 'Content-Type': 'application/json' } }
            );
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Make API service call
     * @param method Method name
     * @param params Parameters for the API call
     */
    async apiServiceCall(method: string, params: any) {
        try {
            const response = await axios.post(
                `/m/oauth2/api/com/${method}`,
                params,
                { headers: { 'Content-Type': 'application/json' } }
            );
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Get token for authentication
     * @param credentials Object containing grant_type and client_id, plus either username/password for password grant or refresh_token for refresh token grant
     */
    async getToken(credentials: {
        grant_type: string,
        username?: string,
        password?: string,
        refresh_token?: string,
        client_id: string
    }) {
        try {
            const response = await axios.post(
                '/m/oauth2/token',
                credentials,
                { headers: { 'Content-Type': 'application/json' } }
            );
            
            // Store tokens when retrieved
            if (response.data.access_token) {
                this.accessToken = response.data.access_token;
            }
            if (response.data.refresh_token) {
                this.refreshToken = response.data.refresh_token;
            }
            
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Revoke an authentication token
     * @param token Token to revoke
     */
    async revokeToken(token: string) {
        try {
            const response = await axios.post(
                '/m/oauth2/revoke',
                { token },
                { headers: { 'Content-Type': 'application/json' } }
            );
            
            // If we're revoking the current access token, clear it
            if (token === this.accessToken) {
                this.accessToken = null;
            }
            
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Complete the password reset process
     * @param email Email address
     * @param code Validation code
     * @param newPassword New password to set
     * @returns Object with success status and message
     */
    async resetPassword(email: string, code: string, newPassword: string) {
        try {
            const response = await axios.post(
                '/m/oauth2/com/reset_password',
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Change password for authenticated user
     * @param userId User ID
     * @param currentPassword Current password
     * @param newPassword New password to set
     * @returns Object with success status and message
     */
    async changePassword(userId: string, currentPassword: string, newPassword: string) {
        try {
            const response = await axios.post(
                '/m/oauth2/com/change_password',
                { user_id: userId, current_password: currentPassword, new_password: newPassword },
                { 
                    headers: { 
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.accessToken}`
                    } 
                }
            );
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Get the roles assigned to a user
     * @param userId User ID
     * @returns Array of role names
     */
    async getUserRoles(userId: string): Promise<string[]> {
        try {
            const response = await axios.get(
                '/m/oauth2/com/get_user_roles',
                {
                    params: { user_id: userId },
                    headers: { 
                        'Authorization': `Bearer ${this.accessToken}`
                    }
                }
            );
            return response.data.roles || [];
        } catch (error) {
            throw error;
        }
    }

    /**
     * Get the permissions assigned to a user
     * @param userId User ID
     * @returns Array of permission names
     */
    async getUserPermissions(userId: string): Promise<string[]> {
        try {
            const response = await axios.get(
                '/m/oauth2/com/get_user_permissions',
                {
                    params: { user_id: userId },
                    headers: { 
                        'Authorization': `Bearer ${this.accessToken}`
                    }
                }
            );
            return response.data.permissions || [];
        } catch (error) {
            throw error;
        }
    }
    
    /**
     * Verify CSRF token against session
     * @param csrfToken The CSRF token to verify
     * @param sessionId The session ID the token should be associated with
     * @returns True if the token is valid for the session, false otherwise
     * @throws Error if there's an error during verification
     */
    async verifyCsrfToken(csrfToken: string, sessionId: string | null): Promise<boolean> {
        try {
            // Handle null sessionId by using empty string
            const safeSessionId = sessionId || '';
            
            const response = await axios.post(
                '/m/oauth2/com/verify_csrf_token',
                { 
                    token: csrfToken, 
                    session_id: safeSessionId 
                },
                { 
                    headers: { 
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.accessToken}`
                    } 
                }
            );
            return response.data.valid === true;
        } catch (error) {
            // Rethrow the error to be handled by the caller
            throw error;
        }
    }
    
    /**
     * Check login attempts to prevent brute force attacks
     * @param username Username or email address used in the login attempt
     * @param domain Domain for the login attempt
     * @returns Object containing remaining attempts and locked out status
     */
    async checkLoginAttempts(username: string, domain: string) {
        try {
            const response = await axios.get(
                '/m/oauth2/com/check_login_attempts',
                { 
                    params: { 
                        username,
                        domain
                    }
                }
            );
            
            // Handle malformed response by providing default values
            return {
                remainingAttempts: response.data.remaining_attempts ?? 0,
                lockedOut: response.data.locked_out ?? false
            };
        } catch (error) {
            throw error;
        }
    }
    
    /**
     * Revoke all tokens for a specific user
     * @param userId User ID to revoke all tokens for
     * @returns Object with success status and number of tokens revoked
     */
    async revokeAllTokens(userId: string) {
        try {
            const response = await axios.post(
                '/m/oauth2/com/revoke_all_tokens',
                { user_id: userId },
                { 
                    headers: { 
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.accessToken}`
                    } 
                }
            );
            
            // If we're revoking tokens for the current user, also clear the local tokens
            if (this.userInfo && this.userInfo.id === userId) {
                this.accessToken = null;
                this.refreshToken = null;
            }
            
            return {
                success: response.data.success,
                tokensRevoked: response.data.tokens_revoked
            };
        } catch (error) {
            throw error;
        }
    }
    
    /**
     * Clear authentication cookies from the client
     * @returns True if cookies were successfully cleared
     */
    clearAuthCookies(): boolean {
        try {
            // Clear local tokens and user data
            this.accessToken = null;
            this.refreshToken = null;
            this.sessionId = null;
            this.userInfo = null;
            
            // Remove cookies from browser if in browser environment
            if (typeof document !== 'undefined') {
                // Common approach to delete cookies by setting expiration in the past
                document.cookie = 'access_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                document.cookie = 'refresh_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                document.cookie = 'session_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            }
            
            return true;
        } catch (error) {
            console.error('Failed to clear auth cookies:', error);
            return false;
        }
    }
    
    /**
     * Invalidate a specific session
     * @param sessionId The ID of the session to invalidate
     * @returns Object with success status
     */
    async invalidateSession(sessionId: string) {
        // Check if session ID is null or empty
        if (!sessionId) {
            return { success: false };
        }
        
        try {
            const response = await axios.post(
                '/m/oauth2/com/invalidate_session',
                { session_id: sessionId },
                { 
                    headers: { 
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.accessToken}`
                    } 
                }
            );
            
            // If this was the current session, clear session ID
            if (sessionId === this.sessionId) {
                this.sessionId = null;
            }
            
            return response.data;
        } catch (error) {
            throw error;
        }
    }
}