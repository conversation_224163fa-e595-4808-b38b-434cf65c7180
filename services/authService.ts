import { AuthRepository } from '~/repositories/authRepository';
import { OktaAuthRepository } from '~/repositories/oktaAuthRepository';
import { isValidEmail, isValidPassword, sanitizeInput, validateIPAddress } from '../utils/validation';

/**
 * Interface for login attempt validation response
 */
interface LoginAttemptValidation {
  allowed: boolean;
  remainingAttempts?: number;
}

/**
 * Service for authentication and user management
 * Supports both traditional authentication and Okta OAuth 2.0 authentication
 */
export class AuthService {
  private authRepository: AuthRepository | null = null;
  private oktaAuthRepository: OktaAuthRepository | null = null;
  private authType: 'traditional' | 'okta' = 'traditional';
  
  /**
   * Create a new AuthService instance with traditional authentication
   * @param authRepository The authentication repository instance
   */
  constructor(authRepository: AuthRepository | OktaAuthRepository) {
    if (authRepository instanceof OktaAuthRepository) {
      this.oktaAuthRepository = authRepository;
      this.authType = 'okta';
    } else {
      this.authRepository = authRepository;
      this.authType = 'traditional';
    }
  }
  
  /**
   * Register a new user
   * @param userData Object containing name, email, and password
   * @returns Object with success status and userId
   * @throws Error if password does not meet strength requirements
   */
  async registerUser(userData: { name: string, email: string, password: string }) {
    // Validate name is not empty
    if (!userData.name || userData.name.trim() === '') {
      throw new Error('Name is required');
    }
    
    // Validate email is not empty
    if (!userData.email || userData.email.trim() === '') {
      throw new Error('Email is required');
    }
    
    // Validate email maximum length
    const MAX_EMAIL_LENGTH = 320; // Standard maximum email length
    if (userData.email.length > MAX_EMAIL_LENGTH) {
      throw new Error('Email exceeds maximum length');
    }
    
    // Validate email format
    if (!isValidEmail(userData.email)) {
      return { success: false, message: 'Invalid email format' };
    }
    
    // Validate password is not empty
    if (!userData.password || userData.password.trim() === '') {
      throw new Error('Password is required');
    }
    
    // Validate password strength - throw error for weak passwords
    if (!isValidPassword(userData.password)) {
      throw new Error('Password does not meet strength requirements');
    }
    
    try {
      // Make sure we're using traditional auth
      if (!this.authRepository) {
        throw new Error('Traditional authentication repository not configured');
      }

      // Call repository method to create account
      const result = await this.authRepository!.createAccount(userData);
      
      // Transform response to match service interface
      return { 
        success: result.success, 
        userId: result.user_id 
      };
    } catch (error) {
      // Throw the error if email already exists or for other specific validation errors
      // that should be propagated as exceptions
      if (error instanceof Error && (
          error.message === 'Email already exists' || 
          error.message.includes('validation')
      )) {
        throw error;
      }
      
      // For other types of errors, return a formatted error response
      return {
        success: false,
        message: error instanceof Error ? error.message : 'An unknown error occurred'
      };
    }
  }
  
  // Using imported isValidEmail from validation utils
  
  // Using imported isValidPassword from validation utils
  
  /**
   * Log in user with email and password or via Okta authentication flow
   * @param email User email (optional for Okta login)
   * @param password User password (optional for Okta login)
   * @returns Object with success status and authentication tokens
   * @throws Error if credentials are invalid, network errors occur, or other authentication errors
   */
  async login(email?: string, password?: string) {
    if (this.authType === 'okta') {
      // Use Okta authentication flow
      try {
        if (!this.oktaAuthRepository) {
          throw new Error('Okta repository not configured');
        }
        
        const result = await this.oktaAuthRepository!.login();
        
        if (!result.success) {
          throw new Error(result.error || 'Okta authentication failed');
        }
        
        // Return success with access token
        return {
          success: true,
          accessToken: this.oktaAuthRepository.getAccessToken(),
          idToken: this.oktaAuthRepository.getIdToken()
        };
      } catch (error) {
        // Handle network errors specially
        if (error instanceof Error && (
            error.name === 'NetworkError' || 
            error.message.includes('Network Error') ||
            error.message.includes('network')
        )) {
          throw new Error('Authentication service is temporarily unavailable');
        }
        
        // Re-throw other errors
        throw error;
      }
    } else {
      // Use traditional authentication flow
      if (!this.authRepository) {
        throw new Error('Traditional authentication repository not configured');
      }
      
      // Check for null/undefined credentials for traditional auth
      if (email === undefined || email === null) {
        throw new Error('Email is required');
      }
      
      if (password === undefined || password === null) {
        throw new Error('Password is required');
      }
      
      // Validate email format
      if (!isValidEmail(email)) {
        throw new Error('Invalid email format');
      }
      
      try {
        // Use standard OAuth2 password grant flow
        const tokenResponse = await this.authRepository!.getToken({
          grant_type: 'password',
          username: email,
          password: password,
          client_id: 'learning_coach_client' // Client ID for the application
        });
        
        return {
          success: true,
          accessToken: tokenResponse.access_token,
          refreshToken: tokenResponse.refresh_token
        };
      } catch (error) {
        // Handle network errors specially
        if (error instanceof Error && (
            error.name === 'NetworkError' || 
            error.message.includes('Network Error') ||
            error.message.includes('network') ||
            error.message.includes('ECONNREFUSED') ||
            error.message.includes('ETIMEDOUT')
        )) {
          throw new Error('Authentication service is temporarily unavailable');
        }
        
        // Handle API errors with specific status codes
        if (error instanceof Error && 
            error.name === 'AxiosError' && 
            (error as any).response?.status) {
          const response = (error as any).response;
          
          // Extract the error message from the response if available
          const errorMessage = response.data?.message || 
                              response.data?.error || 
                              `API error: ${response.status}`;
                              
          throw new Error(errorMessage);
        }
        
        // Re-throw other errors
        throw error;
      }
    }
  }
  
  /**
   * Verify if a token is valid
   * @param token The token to verify
   * @returns True if the token is valid, false otherwise
   */
  async verifyToken(token: string): Promise<boolean> {
    if (this.authType === 'okta') {
      if (!this.oktaAuthRepository) {
        throw new Error('Okta repository not configured');
      }
      
      // For Okta, check if the user is authenticated and the token matches
      const isAuth = await this.oktaAuthRepository!.isAuthenticated();
      if (!isAuth) return false;
      
      const currentToken = this.oktaAuthRepository!.getAccessToken();
      return token === currentToken;
    } else {
      if (!this.authRepository) {
        throw new Error('Traditional authentication repository not configured');
      }
      
      // Check if the provided token matches the stored access token
      const currentToken = this.authRepository!.getAccessToken();
      return token === currentToken;
    }
  }
  
  /**
   * Validate an access token
   * @param token The token to validate
   * @returns True if the token is valid
   * @throws Error if the token is expired or invalid
   */
  async validateToken(token: string): Promise<boolean> {
    // Check if we have a repository configured
    if (!this.authRepository) {
      throw new Error('Traditional authentication repository not configured');
    }
    
    // Check if the provided token matches the stored access token and is not expired
    const currentToken = this.authRepository!.getAccessToken();
    
    // If the token is null or undefined, it indicates an expired token
    if (!currentToken) {
      throw new Error('Token expired');
    }
    
    // If the token doesn't match, it's invalid
    if (token !== currentToken) {
      throw new Error('Invalid token');
    }
    
    return true;
  }
  
  /**
   * Refresh the access token using the refresh token
   * @returns Object with success status, new access token and refresh token
   * @throws Error if refresh token is invalid or expired
   */
  async refreshToken() {
    if (this.authType === 'okta') {
      if (!this.oktaAuthRepository) {
        throw new Error('Okta repository not configured');
      }
      
      try {
        // Use Okta's token refresh mechanism
        const success = await this.oktaAuthRepository!.refreshTokens();
        
        if (!success) {
          throw new Error('Failed to refresh tokens');
        }
        
        return {
          success: true,
          accessToken: this.oktaAuthRepository.getAccessToken(),
          idToken: this.oktaAuthRepository.getIdToken()
        };
      } catch (error) {
        // Handle error
        throw new Error('Session expired, please login again');
      }
    } else {
      if (!this.authRepository) {
        throw new Error('Traditional authentication repository not configured');
      }
      
      // Get the current refresh token
      const refreshToken = this.authRepository.getRefreshToken();
      
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }
      
      try {
        // Use the OAuth2 refresh token grant flow
        const tokenResponse = await this.authRepository.getToken({
          grant_type: 'refresh_token',
          refresh_token: refreshToken,
          client_id: 'learning_coach_client' // Client ID for the application
        });
        
        return {
          success: true,
          accessToken: tokenResponse.access_token,
          refreshToken: tokenResponse.refresh_token
        };
      } catch (error) {
        // Handle expired refresh token specially
        if (error instanceof Error && 
            error.name === 'AxiosError' && 
            (error as any).response?.status === 401) {
          throw new Error('Session expired, please login again');
        }
        
        // Re-throw other errors
        throw error;
      }
    }
  }
  
  /**
   * Request a password reset for a user
   * @param email Email address of the user
   * @returns Object with success status and message
   */
  async requestPasswordReset(email: string) {
    // Validate email format
    if (!isValidEmail(email)) {
      return { success: false, message: 'Invalid email format' };
    }
    
    // Make sure we're using traditional auth
    if (!this.authRepository) {
      throw new Error('Traditional authentication repository not configured');
    }
    
    // Call repository method to send password reset request
    return await this.authRepository!.resetPasswordSendRequest(email);
  }

  /**
   * Validate a password reset code
   * @param email Email address of the user
   * @param code Reset code to validate
   * @returns Object with success status and validation result
   * @throws Error if the reset code is invalid
   */
  async validateResetCode(email: string, code: string) {
    // Validate email format
    if (!isValidEmail(email)) {
      return { success: false, message: 'Invalid email format' };
    }
    
    // Make sure we're using traditional auth
    if (!this.authRepository) {
      throw new Error('Traditional authentication repository not configured');
    }
    
    // Call repository method to check the reset code
    const result = await this.authRepository!.resetPasswordCheckCode(email, code);
    
    // Check if the code is valid
    if (result.success && !result.valid) {
      throw new Error('Invalid reset code');
    }
    
    return result;
  }

  /**
   * Complete the password reset process
   * @param email Email address of the user
   * @param code Reset code to validate
   * @param newPassword New password to set
   * @returns Object with success status and message
   * @throws Error if the reset code is invalid or the password doesn't meet requirements
   */
  async completePasswordReset(email: string, code: string, newPassword: string) {
    // Make sure we're using traditional auth
    if (!this.authRepository) {
      throw new Error('Traditional authentication repository not configured');
    }
    
    // Validate the reset code first
    const codeValidation = await this.authRepository!.resetPasswordCheckCode(email, code);
    
    if (!codeValidation.success || !codeValidation.valid) {
      throw new Error('Invalid reset code');
    }
    
    // Validate the new password strength
    if (!isValidPassword(newPassword)) {
      throw new Error('Password does not meet strength requirements');
    }
    
    // Call repository method to reset the password
    return await this.authRepository!.resetPassword(email, code, newPassword);
  }
  
  /**
   * Change password for authenticated user
   * @param userId User ID of the authenticated user
   * @param currentPassword Current password for verification
   * @param newPassword New password to set
   * @returns Object with success status and message
   * @throws Error if the password does not meet strength requirements
   */
  async changePassword(userId: string, currentPassword: string, newPassword: string) {
    // Validate the new password strength
    if (!isValidPassword(newPassword)) {
      throw new Error('Password does not meet strength requirements');
    }
    
    // Make sure we're using traditional auth
    if (!this.authRepository) {
      throw new Error('Traditional authentication repository not configured');
    }
    
    // Call repository method to change the password
    return await this.authRepository!.changePassword(userId, currentPassword, newPassword);
  }

  /**
   * Check if a user has a specific role
   * @param userId User ID to check
   * @param role Role name to verify
   * @returns True if the user has the specified role, false otherwise
   * @throws Error if there's an authentication error
   */
  async hasRole(userId: string, role: string): Promise<boolean> {
    try {
      // Get the appropriate repository based on auth type
      if (this.authType === 'okta' && this.oktaAuthRepository) {
        const userRoles = await this.oktaAuthRepository!.getUserRoles(userId);
        return userRoles.includes(role);
      } else if (this.authRepository) {
        // Get all roles for the user from repository
        const userRoles = await this.authRepository!.getUserRoles(userId);
      
        // Check if the requested role is in the user's roles array
        return userRoles.includes(role);
      } else {
        throw new Error('No authentication repository configured');
      }
    } catch (error) {
      // Log authentication errors for monitoring purposes
      console.error(`Authentication error when checking role '${role}' for user ${userId}`, error);
      // Re-throw the error for proper error handling up the chain
      throw error;
    }
  }

  /**
   * Check if a user has any of the required permissions
   * @param userId User ID to check
   * @param permissions Array of permission names to verify
   * @returns True if the user has any of the specified permissions, false otherwise
   */
  async hasPermission(userId: string, permissions: string[]): Promise<boolean> {
    // Get the appropriate repository based on auth type
    if (this.authType === 'okta' && this.oktaAuthRepository) {
      const userPermissions = await this.oktaAuthRepository!.getUserPermissions(userId);
      return permissions.some(permission => userPermissions.includes(permission));
    } else if (this.authRepository) {
      // Get all permissions for the user from repository
      const userPermissions = await this.authRepository!.getUserPermissions(userId);
    
      // Check if any of the requested permissions are in the user's permissions array
      return permissions.some(permission => userPermissions.includes(permission));
    } else {
      throw new Error('No authentication repository configured');
    }
  }
  
  /**
   * Verify CSRF token against session
   * @param csrfToken The CSRF token to verify
   * @param sessionId The session ID the token should be associated with
   * @returns True if the token is valid for the session
   * @throws Error if the CSRF token is invalid
   */
  async verifyCsrf(csrfToken: string, sessionId: string): Promise<boolean> {
    // Make sure we're using traditional auth
    if (!this.authRepository) {
      throw new Error('Traditional authentication repository not configured');
    }
    
    // Call repository method to verify the CSRF token
    const isValid = await this.authRepository!.verifyCsrfToken(csrfToken, sessionId);
    
    // Throw an error if the token is invalid
    if (!isValid) {
      throw new Error('Invalid CSRF token');
    }
    
    return isValid;
  }
  
  /**
   * Validate login attempts to prevent brute force attacks
   * @param ip IP address of the login attempt
   * @param email Email address used in the login attempt
   * @returns Object with allowed status and remaining attempts
   * @throws Error if too many failed login attempts detected
   */
  async validateLoginAttempt(ip: string, email: string): Promise<LoginAttemptValidation> {
    // Make sure we're using traditional auth
    if (!this.authRepository) {
      throw new Error('Traditional authentication repository not configured');
    }
    
    // Check login attempts through repository
    const result = await this.authRepository!.checkLoginAttempts(ip, email);
    
    // If locked out due to too many attempts, throw error
    if (result.lockedOut) {
      throw new Error('Too many failed login attempts');
    }
    
    // Use the remainingAttempts directly from the result
    const remainingAttempts = result.remainingAttempts;
    
    // Return validation result
    return {
      allowed: true,
      remainingAttempts
    };
  }
  
  /**
   * Sanitize user input to prevent XSS attacks
   * @param input User input to sanitize
   * @returns Sanitized input string
   */
  sanitizeInput(input: string): string {
    return sanitizeInput(input);
  }

  /**
   * Validate IP address format
   * @param ip IP address to validate
   * @returns True if IP address is valid, false otherwise
   */
  validateIPAddress(ip: string): boolean {
    return validateIPAddress(ip);
  }

  /**
   * Validate email format including support for international characters
   * @param email Email to validate
   * @returns True if email is valid, false otherwise
   */
  validateEmailFormat(email: string): boolean {
    return isValidEmail(email);
  }

  /**
   * Initialize Okta authentication (only applicable when using OktaAuthRepository)
   * @param config Okta configuration options
   * @throws Error if not using Okta authentication
   */
  async initializeOkta(config: {
    clientId: string;
    redirectUri: string;
    endSessionRedirectUri: string;
    discoveryUri: string;
    scopes: string[];
    requireHardwareBackedKeyStore?: boolean;
  }): Promise<void> {
    if (this.authType !== 'okta' || !this.oktaAuthRepository) {
      throw new Error('Cannot initialize Okta with traditional authentication repository');
    }
    
    return await this.oktaAuthRepository!.configure(config);
  }
  
  /**
   * Logout the current user by revoking their access token and invalidating their session
   * @param clearCookies If true, also clears auth cookies
   * @returns Object with success status
   * @throws Error if no active session is found
   */
  async logout(clearCookies: boolean = false) {
    // Get the current access token
    let accessToken;
    if (this.authRepository) {
      accessToken = this.authRepository!.getAccessToken();
    } else if (this.oktaAuthRepository) {
      accessToken = this.oktaAuthRepository!.getAccessToken();
    } else {
      throw new Error('No authentication repository found');
    }
    
    // Throw an error if no token exists (no active session)
    if (!accessToken) {
      throw new Error('No active session found');
    }
    
    // Get the current session ID if using traditional auth
    let sessionId = null;
    if (this.authRepository) {
      sessionId = this.authRepository!.getSessionId();
    }
    
    try {
      // Revoke the token through repository
      let result;
      if (this.authType === 'okta' && this.oktaAuthRepository) {
        await this.oktaAuthRepository!.revokeAccessToken();
        result = { success: true };
      } else if (this.authRepository) {
        result = await this.authRepository!.revokeToken(accessToken);
      } else {
        throw new Error('No authentication repository configured');
      }
      
      // Invalidate the session in the database
      if (sessionId && this.authRepository) {
        await this.authRepository!.invalidateSession(sessionId);
      }
      
      // Clear auth cookies if requested
      if (clearCookies && this.authRepository) {
        this.authRepository!.clearAuthCookies();
      }
      
      // Validate that the response has the expected format
      // We expect a response with at least a 'success' property
      if (typeof result !== 'object' || result === null || typeof result.success !== 'boolean') {
        return { success: false, error: 'Unexpected response format' };
      }
      
      return result;
    } catch (error) {
      // Handle any errors during logout
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred during logout' 
      };
    }
  }
  
  /**
   * Logout from all devices by revoking all tokens for a user
   * @param userId The user ID to revoke all tokens for
   * @returns Object with success status and number of tokens revoked
   */
  async logoutAllDevices(userId: string) {
    // Make sure we're using traditional auth
    if (!this.authRepository) {
      throw new Error('Traditional authentication repository not configured');
    }
    
    // Revoke all tokens for the user through repository
    return await this.authRepository!.revokeAllTokens(userId);
  }
  
  /**
   * Request password reset with automatic retries on failure
   * @param email Email address of the user
   * @param maxRetries Maximum number of retry attempts
   * @returns Object with success status and message
   * @throws Error if all retry attempts fail
   */
  async requestPasswordResetWithRetry(email: string, maxRetries: number) {
    // Validate email format
    if (!isValidEmail(email)) {
      return { success: false, message: 'Invalid email format' };
    }
    
    let attempts = 0;
    let lastError: Error | null = null;
    
    // Try the request up to maxRetries times
    while (attempts < maxRetries) {
      try {
        // Make sure we're using traditional auth
        if (!this.authRepository) {
          throw new Error('Traditional authentication repository not configured');
        }
        
        // Attempt to send password reset request
        const result = await this.authRepository!.resetPasswordSendRequest(email);
        return result;
      } catch (error) {
        // Store the error for potential re-throw later
        lastError = error instanceof Error ? error : new Error(String(error));
        attempts++;
        
        // If we've reached max retries, don't continue
        if (attempts >= maxRetries) {
          break;
        }
        
        // Optional: Add a small delay between retries (exponential backoff)
        // await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempts) * 100));
      }
    }
    
    // If we've exhausted all retries, throw the last error encountered
    if (lastError) {
      throw lastError;
    }
    
    // This should never be reached if lastError is set, but TypeScript doesn't know that
    return { success: false, message: 'Failed after multiple retry attempts' };
  }
}