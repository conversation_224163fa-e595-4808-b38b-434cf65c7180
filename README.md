# Learning Coach Community
React Native/Expo app for the Learning Coach Community

## Directories
- `assets` (@assets)
  - `images`
  - `fonts`
- `src` (@/) - The main app directory
  - `api/` (@/api): Contains API calls and network-related code.
  - `components/` (@/components): Reusable UI components.
  - `config/` (@/config): Configuration files, constants, and environment variables.
  - `hooks/` (@/hooks): Custom hooks.
  - `navigation/` (@/navigation): Navigation setup and configurations.
  - `repositories/` (@/repositories): Data access layers, interfaces for APIs, databases, etc.
  - `screens/` (@/screens): Screen components.
  - `services/` (@/services): Business logic, interacting with repositories.
  - `styles/` (@/styles): Styling and theming.
  - `usecases/` (@/usecases): Specific application use cases or business rules.
  - `utils/` (@/utils): Utility functions.

## Get started

1. Install dependencies

   ```bash
   npm install
   ```

2. Start the app

   ```bash
   npx expo start
   ```

In the output, you'll find options to open the app in a

- [development build](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

You can start developing by editing the files inside the **app** directory. This project uses [file-based routing](https://docs.expo.dev/router/introduction).


## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.

## Join the community

Join our community of developers creating universal apps.

- [Expo on GitHub](https://github.com/expo/expo): View our open source platform and contribute.
- [Discord community](https://chat.expo.dev): Chat with Expo users and ask questions.
