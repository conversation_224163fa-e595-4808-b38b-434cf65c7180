# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/
dist/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv


# --- React Native / Expo specific ---
# Expo
.expo/
.expo-shared/
expo-env.d.ts

# Metro bundler
.metro-cache/
tmp/
web-build/

# iOS / Xcode
ios/build/
ios/Pods/
ios/.DS_Store
ios/*.xcodeproj/project.xcworkspace/xcuserdata
ios/*.pbxuser
ios/*.mode1v3
ios/*.mode2v3
ios/*.perspectivev3
ios/*.xcuserstate

# Android / Gradle
android/.gradle/
android/app/build/
android/build/
android/.idea/
android/local.properties
android/.cxx/
*.keystore

# VSCode
.vscode/
.history/

# Test coverage
coverage/

# Husky (keep only config files)
.husky/_/

# dotenv env files
.env
.env.*

# Misc
*.swp
*.swo

# @generated expo-cli sync-2b81b286409207a5da26e14c78851eb30d8ccbdb
# The following patterns were generated by expo-cli

expo-env.d.ts
# @end expo-cli

