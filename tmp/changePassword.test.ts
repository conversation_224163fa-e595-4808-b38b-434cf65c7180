// Temporary test file for password change functionality
import { AuthService } from '../services/authService';
import { AuthRepository } from '../repositories/authRepository';

// Mock the AuthRepository
jest.mock('../repositories/authRepository');

describe('AuthService - Password Change', () => {
  let authService: AuthService;
  let mockAuthRepository: jest.Mocked<AuthRepository>;
  
  beforeEach(() => {
    mockAuthRepository = new AuthRepository() as jest.Mocked<AuthRepository>;
    authService = new AuthService(mockAuthRepository);
    jest.clearAllMocks();
  });
  
  it('should change password successfully for authenticated user', async () => {
    // Arrange
    const userId = '123';
    const currentPassword = 'CurrentPassword123!';
    const newPassword = 'NewPassword123!';
    
    mockAuthRepository.changePassword.mockResolvedValue({
      success: true,
      message: 'Password changed successfully'
    });
    
    // Act
    const result = await authService.changePassword(userId, currentPassword, newPassword);
    
    // Assert
    expect(mockAuthRepository.changePassword).toHaveBeenCalledWith(userId, currentPassword, newPassword);
    expect(result).toEqual({
      success: true,
      message: 'Password changed successfully'
    });
  });
  
  it('should reject password change with incorrect current password', async () => {
    // Arrange
    const userId = '123';
    const currentPassword = 'WrongPassword123!';
    const newPassword = 'NewPassword123!';
    
    const error = new Error('Current password is incorrect');
    mockAuthRepository.changePassword.mockRejectedValue(error);
    
    // Act & Assert
    await expect(authService.changePassword(userId, currentPassword, newPassword)).rejects.toThrow('Current password is incorrect');
    expect(mockAuthRepository.changePassword).toHaveBeenCalledWith(userId, currentPassword, newPassword);
  });
  
  it('should reject password change with weak new password', async () => {
    // Arrange
    const userId = '123';
    const currentPassword = 'CurrentPassword123!';
    const newPassword = 'password'; // Weak password
    
    // Act & Assert
    await expect(authService.changePassword(userId, currentPassword, newPassword)).rejects.toThrow('Password does not meet strength requirements');
    expect(mockAuthRepository.changePassword).not.toHaveBeenCalled();
  });
});
