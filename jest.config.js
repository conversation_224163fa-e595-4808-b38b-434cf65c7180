module.exports = {
	// Global settings that apply to all projects
	coverageDirectory: './coverage',
	setupFilesAfterEnv: ['./jest.setup.js', '@testing-library/jest-native/extend-expect'],
	moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'],

	// Configure moduleNameMapper for path aliases
	moduleNameMapper: {
		'^~/(.*)$': '<rootDir>/$1',
		'^@/(.*)$': '<rootDir>/src/$1',
		'^@assets/(.*)$': '<rootDir>/assets/$1',
		'^@utils/(.*)$': '<rootDir>/utils/$1',
		'^@services/(.*)$': '<rootDir>/services/$1',
		'^@repositories/(.*)$': '<rootDir>/repositories/$1',
		'^@components/(.*)$': '<rootDir>/components/$1',
		'^@hooks/(.*)$': '<rootDir>/hooks/$1',
		'^@constants/(.*)$': '<rootDir>/constants/$1',
	},

	// Define different configurations for different test patterns
	projects: [
		// Default configuration for React Native tests
		{
			displayName: 'react-native',
			preset: 'ts-jest',
			testEnvironment: 'node',
			transform: {
				'^.+\\.(ts|tsx)$': 'ts-jest',
			},
			testMatch: [
				'**/?(*.)+(spec|test|tests).ts?(x)',
				'**/test/**/*.ts?(x)',
				'**/test/**/*_tests.ts?(x)',
			],
			collectCoverageFrom: [
				'**/*.{js,jsx,ts,tsx}',
				'!**/coverage/**',
				'!**/node_modules/**',
				'!**/babel.config.js',
				'!**/jest.setup.js',
				'!**/jest.config.js',
			],
		},
		// Special configuration just for repositories tests
		{
			displayName: 'repositories',
			testEnvironment: 'node',
			transform: {
				'^.+\\.(ts|tsx)$': 'ts-jest',
			},
			testMatch: ['**/test/repositories/**/*.test.ts?(x)'],
			collectCoverageFrom: [
				'repositories/**/*.{ts,tsx}',
				'!**/coverage/**',
				'!**/node_modules/**',
			],
		},
		// Special configuration just for API tests
		{
			displayName: 'api',
			testEnvironment: 'node',
			transform: {
				'^.+\\.(ts|tsx)$': 'ts-jest',
			},
			testMatch: ['**/test/api/*test.{js,tsx}'],
			collectCoverageFrom: [
				'**/*.{js,jsx,ts,tsx}',
				'!**/coverage/**',
				'!**/node_modules/**',
				'!**/babel.config.js',
				'!**/jest.setup.js',
				'!**/jest.config.js',
			],
		},
	],
};
