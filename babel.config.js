module.exports = function (api) {
	api.cache(true);
	return {
		presets: [['babel-preset-expo', { jsxImportSource: 'nativewind' }], 'nativewind/babel'],
		plugins: [
			[
				'module-resolver',
				{
					root: ['./src'],
					alias: {
						'@': './src',
						'@assets': './assets',
						'@utils': './utils',
						'@services': './services',
						'@repositories': './repositories',
						'~': '.',
					},
				},
			],
		],
	};
};
