// TypeScript test file for AuthRepository

import { AuthRepository } from '../../repositories/authRepository';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Test suite for AuthRepository
describe('AuthRepository', () => {
    let authRepository: AuthRepository;
    
    beforeEach(() => {
        authRepository = new AuthRepository();
        jest.clearAllMocks();
    });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    
    // Basic token and user info methods
    describe('Basic token methods', () => {
        it('should get access token successfully', () => {
            const accessToken = authRepository.getAccessToken();
            expect(accessToken).toBeDefined();
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    
        it('should get refresh token successfully', () => {
            const refreshToken = authRepository.getRefreshToken();
            expect(refreshToken).toBeDefined();
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    
        it('should get user info successfully', () => {
            const userInfo = authRepository.getUserInfo();
            expect(userInfo).toBeDefined();
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
        
        it('should get session ID successfully', () => {
            const sessionId = authRepository.getSessionId();
            expect(sessionId).toBeDefined();
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    
    // Test method
    describe('test endpoint', () => {
        it('should call test method successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true } };
            mockedAxios.get.mockResolvedValue(mockResponse);
            
            // Call method
            const response = await authRepository.testApiConnection();
            
            // Assertions
            expect(mockedAxios.get).toHaveBeenCalledWith('/m/oauth2/com/test');
            expect(response).toEqual(mockResponse.data);
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
        
        it('should handle errors in test method', async () => {
            // Mock error
            const mockError = new Error('API Error');
            mockedAxios.get.mockRejectedValue(mockError);
            
            // Call and expect rejection
            await expect(authRepository.testApiConnection()).rejects.toThrow('API Error');
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    
    // Get page endpoint
    describe('get_page endpoint', () => {
        it('should get page successfully', async () => {
            // Mock response
            const mockPageData = { 
                blocks: [{ id: '1', type: 'text' }], 
                cells: [{ id: '2', content: 'Sample' }] 
            };
            const mockResponse = { data: mockPageData };
            mockedAxios.get.mockResolvedValue(mockResponse);
            
            // Call method
            const pageUri = 'test-page';
            const response = await authRepository.getPage(pageUri);
            
            // Assertions
            expect(mockedAxios.get).toHaveBeenCalledWith('/m/oauth2/com/get_page', { 
                params: { uri: pageUri } 
            });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
            expect(response).toEqual(mockPageData);
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
        
        it('should handle errors when getting page', async () => {
            // Mock error
            const mockError = new Error('Page not found');
            mockedAxios.get.mockRejectedValue(mockError);
            
            // Call and expect rejection
            await expect(authRepository.getPage('invalid-page')).rejects.toThrow('Page not found');
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    
    // Reset password send request endpoint
    describe('reset_password_send_request endpoint', () => {
        it('should send reset password request successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Email sent' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const response = await authRepository.resetPasswordSendRequest(email);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password_send_request', 
                { email },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
        
        it('should handle errors in reset password request', async () => {
            // Mock error
            const mockError = new Error('Invalid email');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            await expect(authRepository.resetPasswordSendRequest('invalid@email')).rejects.toThrow('Invalid email');
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    
    // Reset password check code endpoint
    describe('reset_password_check_code endpoint', () => {
        it('should check reset password code successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, valid: true } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const response = await authRepository.resetPasswordCheckCode(email, code);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password_check_code', 
                { email, code },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
        
        it('should handle errors in code validation', async () => {
            // Mock error
            const mockError = new Error('Invalid code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            await expect(authRepository.resetPasswordCheckCode('<EMAIL>', 'wrong-code')).rejects.toThrow('Invalid code');
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    
    // Create account endpoint
    describe('create_account endpoint', () => {
        it('should create account successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, user_id: '123' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const userData = {
                name: 'Test User',
                email: '<EMAIL>',
                password: 'password123'
            };
            const response = await authRepository.createAccount(userData);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/create_account', 
                userData,
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
        
        it('should handle errors in account creation', async () => {
            // Mock error
            const mockError = new Error('Email already exists');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const userData = {
                name: 'Test User',
                email: '<EMAIL>',
                password: 'password123'
            };
            await expect(authRepository.createAccount(userData)).rejects.toThrow('Email already exists');
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    
    // API service call endpoint
    describe('api/com service call endpoint', () => {
        it('should make API service call successfully', async () => {
            // Mock response
            const mockResponse = { data: { result: 'success', data: { id: 1 } } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const method = 'test-method';
            const params = { param1: 'value1', param2: 'value2' };
            const response = await authRepository.apiServiceCall(method, params);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith(`/m/oauth2/api/com/${method}`, 
                params,
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
        
        it('should handle errors in API service call', async () => {
            // Mock error
            const mockError = new Error('Service unavailable');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            await expect(authRepository.apiServiceCall('invalid-method', {})).rejects.toThrow('Service unavailable');
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    
    // Token endpoint
    describe('token endpoint', () => {
        it('should get token successfully', async () => {
            // Mock response
            const mockTokenResponse = { 
                data: { 
                    access_token: 'abc123', 
                    refresh_token: 'xyz789',
                    expires_in: 3600,
                    token_type: 'Bearer'
                } 
            };
            mockedAxios.post.mockResolvedValue(mockTokenResponse);
            
            // Call method
            const credentials = {
                grant_type: 'password',
                username: '<EMAIL>',
                password: 'password123',
                client_id: 'client123'
            };
            const response = await authRepository.getToken(credentials);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/token', 
                credentials,
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockTokenResponse.data);
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
        
        it('should handle errors in token retrieval', async () => {
            // Mock error
            const mockError = new Error('Invalid credentials');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const credentials = {
                grant_type: 'password',
                username: '<EMAIL>',
                password: 'wrongpassword',
                client_id: 'client123'
            };
            await expect(authRepository.getToken(credentials)).rejects.toThrow('Invalid credentials');
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    
    // Revoke token endpoint
    describe('revoke endpoint', () => {
        it('should revoke token successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const token = 'abc123';
            const response = await authRepository.revokeToken(token);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/revoke', 
                { token },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
        
        it('should handle errors in token revocation', async () => {
            // Mock error
            const mockError = new Error('Invalid token');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            await expect(authRepository.revokeToken('invalid-token')).rejects.toThrow('Invalid token');
        });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
});
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
