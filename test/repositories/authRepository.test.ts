// TypeScript test file for AuthRepository

import { AuthRepository } from '../../repositories/authRepository';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Test suite for AuthRepository
describe('AuthRepository', () => {
    let authRepository: AuthRepository;
    
    beforeEach(() => {
        authRepository = new AuthRepository();
        jest.clearAllMocks();
    });
    
    // Basic token and user info methods
    describe('Basic token methods', () => {
        it('should get access token successfully', () => {
            const accessToken = authRepository.getAccessToken();
            expect(accessToken).toBeDefined();
        });
    
        it('should get refresh token successfully', () => {
            const refreshToken = authRepository.getRefreshToken();
            expect(refreshToken).toBeDefined();
        });
    
        it('should get user info successfully', () => {
            const userInfo = authRepository.getUserInfo();
            expect(userInfo).toBeDefined();
        });
        
        it('should get session ID successfully', () => {
            const sessionId = authRepository.getSessionId();
            expect(sessionId).toBeDefined();
        });
    });
    
    // Test method
    describe('test endpoint', () => {
        it('should call test method successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true } };
            mockedAxios.get.mockResolvedValue(mockResponse);
            
            // Call method
            const response = await authRepository.testApiConnection();
            
            // Assertions
            expect(mockedAxios.get).toHaveBeenCalledWith('/m/oauth2/com/test');
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in test method', async () => {
            // Mock error
            const mockError = new Error('API Error');
            mockedAxios.get.mockRejectedValue(mockError);
            
            // Call and expect rejection
            await expect(authRepository.testApiConnection()).rejects.toThrow('API Error');
        });
    });
    
    // Get page endpoint
    describe('get_page endpoint', () => {
        it('should get page successfully', async () => {
            // Mock response
            const mockPageData = { 
                blocks: [{ id: '1', type: 'text' }], 
                cells: [{ id: '2', content: 'Sample' }] 
            };
            const mockResponse = { data: mockPageData };
            mockedAxios.get.mockResolvedValue(mockResponse);
            
            // Call method
            const pageUri = 'test-page';
            const response = await authRepository.getPage(pageUri);
            
            // Assertions
            expect(mockedAxios.get).toHaveBeenCalledWith('/m/oauth2/com/get_page', { 
                params: { uri: pageUri } 
            });
            expect(response).toEqual(mockPageData);
        });
        
        it('should handle errors when getting page', async () => {
            // Mock error
            const mockError = new Error('Page not found');
            mockedAxios.get.mockRejectedValue(mockError);
            
            // Call and expect rejection
            await expect(authRepository.getPage('invalid-page')).rejects.toThrow('Page not found');
        });
    });
    
    // Reset password send request endpoint
    describe('reset_password_send_request endpoint', () => {
        it('should send reset password request successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Email sent' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const response = await authRepository.resetPasswordSendRequest(email);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password_send_request', 
                { email },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in reset password request', async () => {
            // Mock error
            const mockError = new Error('Invalid email');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            await expect(authRepository.resetPasswordSendRequest('invalid@email')).rejects.toThrow('Invalid email');
        });
    });
    
    // Reset password check code endpoint
    describe('reset_password_check_code endpoint', () => {
        it('should check reset password code successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, valid: true } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const response = await authRepository.resetPasswordCheckCode(email, code);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password_check_code', 
                { email, code },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in code validation', async () => {
            // Mock error
            const mockError = new Error('Invalid code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            await expect(authRepository.resetPasswordCheckCode('<EMAIL>', 'wrong-code')).rejects.toThrow('Invalid code');
        });
    });
    
    // Reset password endpoint
    describe('reset_password endpoint', () => {
        it('should reset password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password reset successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const email = '<EMAIL>';
            const code = '123456';
            const newPassword = 'newPassword123';
            const response = await authRepository.resetPassword(email, code, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/reset_password', 
                { email, code, new_password: newPassword },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password reset', async () => {
            // Mock error
            const mockError = new Error('Invalid reset code');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const email = '<EMAIL>';
            const code = 'invalid-code';
            const newPassword = 'newPassword123';
            await expect(authRepository.resetPassword(email, code, newPassword)).rejects.toThrow('Invalid reset code');
        });
    });
    
    // Create account endpoint
    describe('create_account endpoint', () => {
        it('should create account successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, user_id: '123' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const userData = {
                name: 'Test User',
                email: '<EMAIL>',
                password: 'password123'
            };
            const response = await authRepository.createAccount(userData);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/create_account', 
                userData,
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in account creation', async () => {
            // Mock error
            const mockError = new Error('Email already exists');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const userData = {
                name: 'Test User',
                email: '<EMAIL>',
                password: 'password123'
            };
            await expect(authRepository.createAccount(userData)).rejects.toThrow('Email already exists');
        });
    });
    
    // API service call endpoint
    describe('api/com service call endpoint', () => {
        it('should make API service call successfully', async () => {
            // Mock response
            const mockResponse = { data: { result: 'success', data: { id: 1 } } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const method = 'test-method';
            const params = { param1: 'value1', param2: 'value2' };
            const response = await authRepository.apiServiceCall(method, params);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith(`/m/oauth2/api/com/${method}`, 
                params,
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in API service call', async () => {
            // Mock error
            const mockError = new Error('Service unavailable');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            await expect(authRepository.apiServiceCall('invalid-method', {})).rejects.toThrow('Service unavailable');
        });
    });
    
    // Token endpoint
    describe('token endpoint', () => {
        it('should get token successfully', async () => {
            // Mock response
            const mockTokenResponse = { 
                data: { 
                    access_token: 'abc123', 
                    refresh_token: 'xyz789',
                    expires_in: 3600,
                    token_type: 'Bearer'
                } 
            };
            mockedAxios.post.mockResolvedValue(mockTokenResponse);
            
            // Call method
            const credentials = {
                grant_type: 'password',
                username: '<EMAIL>',
                password: 'password123',
                client_id: 'client123'
            };
            const response = await authRepository.getToken(credentials);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/token', 
                credentials,
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockTokenResponse.data);
        });
        
        it('should handle errors in token retrieval', async () => {
            // Mock error
            const mockError = new Error('Invalid credentials');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const credentials = {
                grant_type: 'password',
                username: '<EMAIL>',
                password: 'wrongpassword',
                client_id: 'client123'
            };
            await expect(authRepository.getToken(credentials)).rejects.toThrow('Invalid credentials');
        });
    });
    
    // Revoke token endpoint
    describe('revoke endpoint', () => {
        it('should revoke token successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const token = 'abc123';
            const response = await authRepository.revokeToken(token);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/revoke', 
                { token },
                { headers: { 'Content-Type': 'application/json' } }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in token revocation', async () => {
            // Mock error
            const mockError = new Error('Invalid token');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            await expect(authRepository.revokeToken('invalid-token')).rejects.toThrow('Invalid token');
        });
    });
    
    // Change password endpoint
    describe('change_password endpoint', () => {
        it('should change password successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true, message: 'Password changed successfully' } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Setup access token
            authRepository['accessToken'] = 'test-access-token';
            
            // Call method
            const userId = 'user123';
            const currentPassword = 'currentPass123';
            const newPassword = 'newPass456';
            const response = await authRepository.changePassword(userId, currentPassword, newPassword);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/change_password', 
                { user_id: userId, current_password: currentPassword, new_password: newPassword },
                { 
                    headers: { 
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test-access-token'
                    } 
                }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in password change', async () => {
            // Mock error
            const mockError = new Error('Invalid current password');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Setup access token
            authRepository['accessToken'] = 'test-access-token';
            
            // Call and expect rejection
            const userId = 'user123';
            const wrongPassword = 'wrongPass';
            const newPassword = 'newPass456';
            await expect(authRepository.changePassword(userId, wrongPassword, newPassword)).rejects.toThrow('Invalid current password');
        });
        
        it('should handle unauthorized access in password change', async () => {
            // Mock error for unauthorized access
            const mockError = new Error('Unauthorized') as Error & { response?: { status: number } };
            mockError.response = { status: 401 };
            mockedAxios.post.mockRejectedValue(mockError);
            
            // No access token set (unauthorized)
            authRepository['accessToken'] = null;
            
            // Call and expect rejection
            const userId = 'user123';
            const currentPassword = 'currentPass123';
            const newPassword = 'newPass456';
            await expect(authRepository.changePassword(userId, currentPassword, newPassword)).rejects.toThrow('Unauthorized');
        });
    });
    
    // User roles endpoint
    describe('get_user_roles endpoint', () => {
        it('should get user roles successfully', async () => {
            // Mock response
            const mockRoles = ['admin', 'editor', 'user'];
            const mockResponse = { data: { roles: mockRoles } };
            mockedAxios.get.mockResolvedValue(mockResponse);
            
            // Setup access token
            authRepository['accessToken'] = 'test-access-token';
            
            // Call method
            const userId = 'user123';
            const response = await authRepository.getUserRoles(userId);
            
            // Assertions
            expect(mockedAxios.get).toHaveBeenCalledWith('/m/oauth2/com/get_user_roles', {
                params: { user_id: userId },
                headers: { 'Authorization': 'Bearer test-access-token' }
            });
            expect(response).toEqual(mockRoles);
        });
        
        it('should handle errors in getting user roles', async () => {
            // Mock error
            const mockError = new Error('User not found');
            mockedAxios.get.mockRejectedValue(mockError);
            
            // Setup access token
            authRepository['accessToken'] = 'test-access-token';
            
            // Call and expect rejection
            await expect(authRepository.getUserRoles('invalid-user')).rejects.toThrow('User not found');
        });
        
        it('should handle unauthorized access when getting user roles', async () => {
            // Mock error for unauthorized access
            const mockError = new Error('Unauthorized') as Error & { response?: { status: number } };
            mockError.response = { status: 401 };
            mockedAxios.get.mockRejectedValue(mockError);
            
            // No access token set (unauthorized)
            authRepository['accessToken'] = null;
            
            // Call and expect rejection
            const userId = 'user123';
            await expect(authRepository.getUserRoles(userId)).rejects.toThrow('Unauthorized');
        });
        
        it('should return empty array when roles are not defined', async () => {
            // Mock response with no roles property
            const mockResponse = { data: {} };
            mockedAxios.get.mockResolvedValue(mockResponse);
            
            // Setup access token
            authRepository['accessToken'] = 'test-access-token';
            
            // Call method
            const userId = 'user123';
            const response = await authRepository.getUserRoles(userId);
            
            // Assertions
            expect(response).toEqual([]);
        });
    });
    
    // User permissions endpoint
    describe('get_user_permissions endpoint', () => {
        it('should get user permissions successfully', async () => {
            // Mock response
            const mockPermissions = ['read', 'write', 'delete'];
            const mockResponse = { data: { permissions: mockPermissions } };
            mockedAxios.get.mockResolvedValue(mockResponse);
            
            // Setup access token
            authRepository['accessToken'] = 'test-access-token';
            
            // Call method
            const userId = 'user123';
            const response = await authRepository.getUserPermissions(userId);
            
            // Assertions
            expect(mockedAxios.get).toHaveBeenCalledWith('/m/oauth2/com/get_user_permissions', {
                params: { user_id: userId },
                headers: { 'Authorization': 'Bearer test-access-token' }
            });
            expect(response).toEqual(mockPermissions);
        });
        
        it('should handle errors in getting user permissions', async () => {
            // Mock error
            const mockError = new Error('User not found');
            mockedAxios.get.mockRejectedValue(mockError);
            
            // Setup access token
            authRepository['accessToken'] = 'test-access-token';
            
            // Call and expect rejection
            await expect(authRepository.getUserPermissions('invalid-user')).rejects.toThrow('User not found');
        });
        
        it('should handle unauthorized access when getting user permissions', async () => {
            // Mock error for unauthorized access
            const mockError = new Error('Unauthorized') as Error & { response?: { status: number } };
            mockError.response = { status: 401 };
            mockedAxios.get.mockRejectedValue(mockError);
            
            // No access token set (unauthorized)
            authRepository['accessToken'] = null;
            
            // Call and expect rejection
            const userId = 'user123';
            await expect(authRepository.getUserPermissions(userId)).rejects.toThrow('Unauthorized');
        });
        
        it('should return empty array when permissions are not defined', async () => {
            // Mock response with no permissions property
            const mockResponse = { data: {} };
            mockedAxios.get.mockResolvedValue(mockResponse);
            
            // Setup access token
            authRepository['accessToken'] = 'test-access-token';
            
            // Call method
            const userId = 'user123';
            const response = await authRepository.getUserPermissions(userId);
            
            // Assertions
            expect(response).toEqual([]);
        });
    });
    
    // CSRF token verification endpoint
    describe('verify_csrf_token endpoint', () => {
        it('should verify CSRF token successfully', async () => {
            // Mock response
            const mockResponse = { data: { valid: true } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Setup access token
            authRepository['accessToken'] = 'test-access-token';
            
            // Call method
            const token = 'csrf-token-123';
            const sessionId = 'session-id-123'; // Added sessionId parameter
            const response = await authRepository.verifyCsrfToken(token, sessionId);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/verify_csrf_token', 
                { token, session_id: sessionId },
                { 
                    headers: { 
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test-access-token'
                    } 
                }
            );
            expect(response).toEqual(true);
        });
        
        it('should return false for invalid CSRF token', async () => {
            // Mock response for invalid token
            const mockResponse = { data: { valid: false } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Setup access token
            authRepository['accessToken'] = 'test-access-token';
            
            // Call method
            const token = 'invalid-csrf-token';
            const sessionId = 'session-id-123'; // Added sessionId parameter
            const response = await authRepository.verifyCsrfToken(token, sessionId);
            
            // Assertions
            expect(response).toEqual(false);
        });
        
        it('should handle errors in CSRF token verification', async () => {
            // Mock error
            const mockError = new Error('Server error');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Setup access token
            authRepository['accessToken'] = 'test-access-token';
            
            // Call and expect rejection
            const token = 'error-token';
            const sessionId = 'session-id-123'; // Added sessionId parameter
            await expect(authRepository.verifyCsrfToken(token, sessionId)).rejects.toThrow('Server error');
        });
        
        it('should handle unauthorized access when verifying CSRF token', async () => {
            // Mock error for unauthorized access
            const mockError = new Error('Unauthorized') as Error & { response?: { status: number } };
            mockError.response = { status: 401 };
            mockedAxios.post.mockRejectedValue(mockError);
            
            // No access token set (unauthorized)
            authRepository['accessToken'] = null;
            
            // Call and expect rejection
            const token = 'csrf-token-123';
            const sessionId = 'session-id-123'; // Added sessionId parameter
            await expect(authRepository.verifyCsrfToken(token, sessionId)).rejects.toThrow('Unauthorized');
        });
        
        it('should use stored sessionId when not explicitly provided', async () => {
            // Mock response
            const mockResponse = { data: { valid: true } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Setup access token and sessionId
            authRepository['accessToken'] = 'test-access-token';
            authRepository['sessionId'] = 'internal-session-id';
            
            // Call method with token and the stored sessionId from the repository
            const token = 'csrf-token-123';
            const response = await authRepository.verifyCsrfToken(token, authRepository['sessionId'] as string);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/verify_csrf_token', 
                { token, session_id: 'internal-session-id' },
                { 
                    headers: { 
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test-access-token'
                    } 
                }
            );
            expect(response).toEqual(true);
        });
        
        it('should handle null sessionId by using empty string', async () => {
            // Mock response
            const mockResponse = { data: { valid: true } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Setup access token and clear sessionId
            authRepository['accessToken'] = 'test-access-token';
            authRepository['sessionId'] = null;
            
            // Call method with token and empty string for sessionId
            const token = 'csrf-token-123';
            const response = await authRepository.verifyCsrfToken(token, '');
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/verify_csrf_token', 
                { token, session_id: '' },
                { 
                    headers: { 
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test-access-token'
                    } 
                }
            );
            expect(response).toEqual(true);
        });
    });
    
    // Check login attempts endpoint
    describe('check_login_attempts endpoint', () => {
        it('should check login attempts successfully', async () => {
            // Mock response with remaining attempts
            const mockResponse = { data: { remaining_attempts: 3, locked_out: false } };
            mockedAxios.get.mockResolvedValue(mockResponse);
            
            // Call method
            const username = '<EMAIL>';
            const domain = 'example.com'; // Adding domain parameter
            const response = await authRepository.checkLoginAttempts(username, domain);
            
            // Assertions
            expect(mockedAxios.get).toHaveBeenCalledWith('/m/oauth2/com/check_login_attempts', {
                params: { username, domain }
            });
            expect(response).toEqual({
                remainingAttempts: 3,
                lockedOut: false
            });
        });
        
        it('should handle locked out user', async () => {
            // Mock response for locked out user
            const mockResponse = { data: { remaining_attempts: 0, locked_out: true } };
            mockedAxios.get.mockResolvedValue(mockResponse);
            
            // Call method
            const username = '<EMAIL>';
            const domain = 'example.com';
            const response = await authRepository.checkLoginAttempts(username, domain);
            
            // Assertions
            expect(response).toEqual({
                remainingAttempts: 0,
                lockedOut: true
            });
        });
        
        it('should handle errors in checking login attempts', async () => {
            // Mock error
            const mockError = new Error('Server error');
            mockedAxios.get.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const username = '<EMAIL>';
            const domain = 'example.com';
            await expect(authRepository.checkLoginAttempts(username, domain)).rejects.toThrow('Server error');
        });
        
        it('should handle malformed response', async () => {
            // Mock malformed response (missing expected properties)
            const mockResponse = { data: {} };
            mockedAxios.get.mockResolvedValue(mockResponse);
            
            // Call method
            const username = '<EMAIL>';
            const domain = 'example.com';
            const response = await authRepository.checkLoginAttempts(username, domain);
            
            // Assertions - should provide default values
            expect(response).toEqual({
                remainingAttempts: 0,
                lockedOut: false
            });
        });
        
        it('should handle non-existent username', async () => {
            // Mock response for non-existent user (might still return a valid response)
            const mockResponse = { data: { remaining_attempts: 5, locked_out: false } };
            mockedAxios.get.mockResolvedValue(mockResponse);
            
            // Call method
            const username = '<EMAIL>';
            const domain = 'example.com';
            const response = await authRepository.checkLoginAttempts(username, domain);
            
            // Assertions
            expect(mockedAxios.get).toHaveBeenCalledWith('/m/oauth2/com/check_login_attempts', {
                params: { username, domain }
            });
            expect(response).toEqual({
                remainingAttempts: 5,
                lockedOut: false
            });
        });
    });
    
    // Revoke all tokens endpoint
    describe('revoke_all_tokens endpoint', () => {
        it('should revoke all tokens successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Setup access token
            authRepository['accessToken'] = 'test-access-token';
            
            // Call method
            const userId = 'user123';
            const response = await authRepository.revokeAllTokens(userId);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/revoke_all_tokens', 
                { user_id: userId },
                { 
                    headers: { 
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test-access-token'
                    } 
                }
            );
            expect(response).toEqual(mockResponse.data);
        });
        
        it('should handle errors in revoking all tokens', async () => {
            // Mock error
            const mockError = new Error('Server error');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Setup access token
            authRepository['accessToken'] = 'test-access-token';
            
            // Call and expect rejection
            const userId = 'error-user';
            await expect(authRepository.revokeAllTokens(userId)).rejects.toThrow('Server error');
        });
        
        it('should handle unauthorized access when revoking all tokens', async () => {
            // Mock error for unauthorized access
            const mockError = new Error('Unauthorized') as Error & { response?: { status: number } };
            mockError.response = { status: 401 };
            mockedAxios.post.mockRejectedValue(mockError);
            
            // No access token set (unauthorized)
            authRepository['accessToken'] = null;
            
            // Call and expect rejection
            const userId = 'user123';
            await expect(authRepository.revokeAllTokens(userId)).rejects.toThrow('Unauthorized');
        });
        
        it('should clear internal tokens if revoking tokens for current user', async () => {
            // Mock response
            const mockResponse = { data: { success: true } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Setup tokens and user info
            const userId = 'current-user';
            authRepository['accessToken'] = 'test-access-token';
            authRepository['refreshToken'] = 'test-refresh-token';
            authRepository['userInfo'] = { id: userId };
            
            // Call method
            await authRepository.revokeAllTokens(userId);
            
            // Verify tokens were cleared
            expect(authRepository['accessToken']).toBeNull();
            expect(authRepository['refreshToken']).toBeNull();
        });
        
        it('should not clear tokens if revoking for a different user', async () => {
            // Mock response
            const mockResponse = { data: { success: true } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Setup tokens and user info for a different user
            const currentUserId = 'current-user';
            const differentUserId = 'different-user';
            authRepository['accessToken'] = 'test-access-token';
            authRepository['refreshToken'] = 'test-refresh-token';
            authRepository['userInfo'] = { id: currentUserId };
            
            // Call method for different user
            await authRepository.revokeAllTokens(differentUserId);
            
            // Verify tokens were not cleared
            expect(authRepository['accessToken']).toBe('test-access-token');
            expect(authRepository['refreshToken']).toBe('test-refresh-token');
        });
    });
    
    // Invalidate session functionality
    describe('invalidateSession', () => {
        beforeEach(() => {
            // Reset tokens and session before each test
            authRepository['accessToken'] = 'test-access-token';
            authRepository['sessionId'] = 'test-session-id';
        });
        
        it('should invalidate a session successfully', async () => {
            // Mock response
            const mockResponse = { data: { success: true } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Call method
            const sessionId = 'session-to-invalidate';
            const response = await authRepository.invalidateSession(sessionId);
            
            // Assertions
            expect(mockedAxios.post).toHaveBeenCalledWith('/m/oauth2/com/invalidate_session', 
                { session_id: sessionId },
                { 
                    headers: { 
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test-access-token'
                    } 
                }
            );
            expect(response).toEqual(mockResponse.data);
            
            // Internal session should not be cleared since it's a different session
            expect(authRepository['sessionId']).toBe('test-session-id');
        });
        
        it('should clear internal session if matching the invalidated session', async () => {
            // Mock response
            const mockResponse = { data: { success: true } };
            mockedAxios.post.mockResolvedValue(mockResponse);
            
            // Setup the session ID that matches internal one
            const sessionId = 'test-session-id'; // Same as internal sessionId
            
            // Call method
            await authRepository.invalidateSession(sessionId);
            
            // Verify internal session was cleared
            expect(authRepository['sessionId']).toBeNull();
        });
        
        it('should handle errors in session invalidation', async () => {
            // Mock error
            const mockError = new Error('Server error');
            mockedAxios.post.mockRejectedValue(mockError);
            
            // Call and expect rejection
            const sessionId = 'error-session';
            await expect(authRepository.invalidateSession(sessionId)).rejects.toThrow('Server error');
            
            // Internal session should not be cleared on error
            expect(authRepository['sessionId']).toBe('test-session-id');
        });
        
        it('should handle unauthorized access when invalidating session', async () => {
            // Mock error for unauthorized access
            const mockError = new Error('Unauthorized') as Error & { response?: { status: number } };
            mockError.response = { status: 401 };
            mockedAxios.post.mockRejectedValue(mockError);
            
            // No access token set (unauthorized)
            authRepository['accessToken'] = null;
            
            // Call and expect rejection
            const sessionId = 'session-to-invalidate';
            await expect(authRepository.invalidateSession(sessionId)).rejects.toThrow('Unauthorized');
        });
        
        it('should not throw error if session ID is null or empty', async () => {
            // Call with null session ID
            const response1 = await authRepository.invalidateSession(null as any);
            expect(response1).toEqual({ success: false });
            
            // Call with empty session ID
            const response2 = await authRepository.invalidateSession('');
            expect(response2).toEqual({ success: false });
            
            // Verify axios was not called in these cases
            expect(mockedAxios.post).not.toHaveBeenCalled();
        });
    });
});
