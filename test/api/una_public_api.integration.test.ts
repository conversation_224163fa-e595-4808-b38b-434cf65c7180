// TypeScript integration test file for UNA API - no mocking
import axios from 'axios';

// Make sure server is running: 
// cd /Users/<USER>/una_cms && ./start_fixed_server.sh

// Base URL for API requests - local development UNA instance
const API_BASE_URL = 'http://localhost:8080';

// Type definitions for API responses
interface TestResponse {
  result: string;
}

interface TokenResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
  scope: string;
  refresh_token: string;
}

interface PageResponse {
  id: number;
  uri: string;
  title: string;
  content: string;
  module: string;
  description: string;
  keywords: string;
}

interface StatusResponse {
  code: number;
}

interface UserResponse {
  id: number;
  profile_name: string;
  email: string;
}

interface ErrorResponse {
  error: string;
  error_description: string;
}

// Integration tests that connect to the real UNA server
// Run with: npm test -- test/api/una_public_api.integration.test.ts
describe('UNA Public API Integration Tests', () => {
  // Set timeout higher for integration tests
  jest.setTimeout(10000);
  
  // Set up common variables
  const clientId = 'api_client_1'; // Common UNA API client ID
  const email = '<EMAIL>'; // Default UNA admin account
  const password = 'una'; // Default UNA admin password
  let token: string;

  // Get a token before running tests that need authentication
  beforeAll(async () => {
    try {
      const response = await axios.post<TokenResponse>(`${API_BASE_URL}/m/oauth2/token`, {
        grant_type: 'password',
        username: email,
        password,
        client_id: clientId
      });
      
      token = response.data.access_token;
      console.log('Authentication successful. Token acquired.');
    } catch (error) {
      console.error('Failed to get authentication token:', error);
      // Tests will still run, but authenticated requests will fail
    }
  });
  
  describe('Test API', () => {
    it('should connect to test endpoint', async () => {
      try {
        const response = await axios.get<TestResponse>(`${API_BASE_URL}/m/oauth2/com/test`);
        
        // Verify the response
        expect(response.status).toBe(200);
        expect(response.data).toHaveProperty('result');
        console.log('Test endpoint response:', response.data);
      } catch (error) {
        console.error('Test endpoint error:', error);
        expect(true).toBe(false); // Test endpoint failed to connect
      }
    });
  });

  // Token API test - using real credentials
  describe('Token API', () => {
    it('should get access token with real credentials', async () => {
      try {
        const response = await axios.post<TokenResponse>(`${API_BASE_URL}/m/oauth2/token`, {
          grant_type: 'password',
          username: email,
          password,
          client_id: clientId
        });
        
        // Verify the response
        expect(response.status).toBe(200);
        expect(response.data).toHaveProperty('access_token');
        expect(response.data).toHaveProperty('expires_in');
        expect(response.data.access_token).toBeTruthy();
        console.log('Token API successful');
      } catch (error) {
        console.error('Token API error:', error);
        expect(true).toBe(false); // Failed to get real token
      }
    });
  });
  
  // Get Page API test - try to get the home page
  describe('Get Page API', () => {
    it('should get home page content', async () => {
      try {
        const response = await axios.get<PageResponse>(`${API_BASE_URL}/m/oauth2/com/get_page`, {
          params: { uri: 'home' }
        });
        
        // Verify the response
        expect(response.status).toBe(200);
        expect(response.data).toHaveProperty('id');
        expect(response.data).toHaveProperty('uri');
        expect(response.data).toHaveProperty('title');
        console.log('Get Page API successful for "home"');
      } catch (error) {
        console.error('Get Page API error:', error);
        expect(true).toBe(false); // Failed to get home page
      }
    });
    
    it('should handle errors for invalid URI', async () => {
      try {
        await axios.get(`${API_BASE_URL}/m/oauth2/com/get_page`, { 
          params: { uri: '/this-page-should-not-exist-12345' }
        });
        
        // If no error is thrown, fail the test
        expect(true).toBe(false); // Expected 404 error for invalid URI but got success
      } catch (error) {
        // Expect error for invalid URI
        if (axios.isAxiosError(error)) {
          expect(error.response?.status).toBe(404);
          console.log('Get Page API correctly returned error for invalid URI');
        } else {
          expect(true).toBe(false); // Unexpected error type
        }
      }
    });
  });

  // Only run these tests if authentication token is available
  describe('Authenticated APIs', () => {
    beforeEach(() => {
      // Skip tests if token isn't available
      if (!token) {
        console.warn('Skipping authenticated tests due to missing token');
        // Skip the test if token is missing
        return;
      }
    });
    
    it('should get current user profile', async () => {
      try {
        const response = await axios.get<UserResponse>(`${API_BASE_URL}/m/oauth2/com/profile`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        
        // Verify the response
        expect(response.status).toBe(200);
        expect(response.data).toHaveProperty('id');
        expect(response.data).toHaveProperty('profile_name');
        console.log('Profile API successful');
      } catch (error) {
        console.error('Profile API error:', error);
        expect(true).toBe(false); // Failed to get user profile
      }
    });
  });
  
  // Test the server's health directly
  describe('Server Health', () => {
    it('should connect to UNA server root URL', async () => {
      try {
        const response = await axios.get(API_BASE_URL, {
          // Increase timeout for potentially slow responses
          timeout: 5000
        });
        
        // Just verify we get a response
        expect(response.status).toBe(200);
        console.log('Server root URL accessible');
      } catch (error) {
        console.error('Server health check error:', error);
        expect(true).toBe(false); // Failed to connect to server root URL
      }
    });
  });
});
