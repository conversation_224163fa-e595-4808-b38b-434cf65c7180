// TypeScript test file for UNA API
import axios from 'axios';
import Mock<PERSON><PERSON>pter from 'axios-mock-adapter';

// Creating a mock for axios
const mock = new MockAdapter(axios);

// Base URL for API requests
const API_BASE_URL = 'http://localhost:8080'; // Local development UNA instance

// Type definitions for API responses
interface TestResponse {
  result: string;
}

interface TokenResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
  scope: string;
  refresh_token: string;
}

interface PageResponse {
  id: number;
  uri: string;
  title: string;
  content: string;
  module: string;
  description: string;
  keywords: string;
}

interface StatusResponse {
  code: number;
}

interface UserResponse {
  id: number;
  profile_name: string;
  email: string;
}

interface ErrorResponse {
  error: string;
  error_description: string;
}

describe('UNA Public API Tests', () => {
  // Reset all mocks after each test
  afterEach(() => {
    mock.reset();
  });
  
  // Set up common variables
  const token = 'cdd7056d0adafa9ead87526ca22367c6b0df8273';
  const clientId = 'client_12345';
  const email = '<EMAIL>';
  const password = 'password123';

  describe('Test API', () => {
    it('should return success for test endpoint', async () => {
      // Mock the API response
      mock.onGet(`${API_BASE_URL}/m/oauth2/com/test`).reply(200, {
        result: 'Test passed.'
      } as TestResponse);

      // Make the API call
      const response = await axios.get<TestResponse>(`${API_BASE_URL}/m/oauth2/com/test`);

      // Verify the response
      expect(response.status).toBe(200);
      expect(response.data).toEqual({ result: 'Test passed.' });
    });

    it('should handle errors for test endpoint', async () => {
      // Mock the API error response
      mock.onGet(`${API_BASE_URL}/m/oauth2/com/test`).reply(400, {
        error: 'Test failed',
        error_description: 'Detailed error description'
      } as ErrorResponse);

      // Make the API call and expect it to be rejected
      await expect(axios.get(`${API_BASE_URL}/m/oauth2/com/test`)).rejects.toThrow();
    });
  });

  // Token API test
  describe('Token API', () => {
    it('should get access token successfully', async () => {
      // Mock successful token response
      mock.onPost(`${API_BASE_URL}/m/oauth2/token`).reply(200, {
        access_token: 'cdd7056d0adafa9ead87526ca22367c6b0df8273',
        expires_in: 3600,
        token_type: 'Bearer',
        scope: 'basic',
        refresh_token: 'c3d7f6f4b7cc640214ae0cba2b194872c3089f1c'
      } as TokenResponse);

      // Make the API call
      const response = await axios.post<TokenResponse>(`${API_BASE_URL}/m/oauth2/token`, {
        grant_type: 'password',
        username: email,
        password,
        client_id: clientId
      });

      // Verify the response
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('access_token');
      expect(response.data).toHaveProperty('expires_in');
    });
  });
  
  // Get Page API test
  describe('Get Page API', () => {
    const pageUri = '/page/about-us';
    
    it('should get page content successfully', async () => {
      // Mock successful page response
      mock.onGet(`${API_BASE_URL}/m/oauth2/com/get_page`, { params: { uri: pageUri } }).reply(200, {
        id: 123,
        uri: '/page/about-us',
        title: 'About Us',
        content: 'Lorem ipsum dolor sit amet',
        module: 'system',
        description: 'About our company',
        keywords: 'about, company, info',
      } as PageResponse);

      // Make the API call
      const response = await axios.get<PageResponse>(`${API_BASE_URL}/m/oauth2/com/get_page`, {
        params: { uri: pageUri }
      });

      // Verify the response
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('id');
      expect(response.data).toHaveProperty('uri');
      expect(response.data).toHaveProperty('title');
    });
    
    it('should handle errors for invalid URI', async () => {
      // Mock error response
      mock.onGet(`${API_BASE_URL}/m/oauth2/com/get_page`, { params: { uri: '/invalid' } }).reply(404, {
        error: 'not_found',
        error_description: 'Page not found'
      } as ErrorResponse);

      // Make the API call and expect it to be rejected
      await expect(axios.get(`${API_BASE_URL}/m/oauth2/com/get_page`, { 
        params: { uri: '/invalid' }
      })).rejects.toThrow();
    });
  });

  // Reset Password Send Request test
  describe('Reset Password Request API', () => {
    it('should send reset password email successfully', async () => {
      // Mock successful response
      mock.onPost(`${API_BASE_URL}/m/oauth2/com/reset_password_send_request`).reply(200, {
        code: 200
      } as StatusResponse);

      // Make the API call
      const response = await axios.post<StatusResponse>(
        `${API_BASE_URL}/m/oauth2/com/reset_password_send_request`,
        { email },
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      // Verify the response
      expect(response.status).toBe(200);
      expect(response.data).toEqual({ code: 200 });
    });
  });
  
  // Reset Password Check Code test
  describe('Reset Password Check Code API', () => {
    const code = '123456';
    
    it('should check reset code successfully', async () => {
      // Mock successful response
      mock.onPost(`${API_BASE_URL}/m/oauth2/com/reset_password_check_code`).reply(200, {
        code: 200
      } as StatusResponse);

      // Make the API call
      const response = await axios.post<StatusResponse>(
        `${API_BASE_URL}/m/oauth2/com/reset_password_check_code`,
        { code, email },
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      // Verify the response
      expect(response.status).toBe(200);
      expect(response.data).toEqual({ code: 200 });
    });
    
    it('should handle invalid code', async () => {
      // Mock error response
      mock.onPost(`${API_BASE_URL}/m/oauth2/com/reset_password_check_code`).reply(400, {
        error: 'invalid_code',
        error_description: 'Invalid code provided'
      } as ErrorResponse);

      // Make the API call and expect it to be rejected
      await expect(axios.post(
        `${API_BASE_URL}/m/oauth2/com/reset_password_check_code`,
        { code: 'wrong', email },
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      )).rejects.toThrow();
    });
  });
  
  // Create Account test
  describe('Create Account API', () => {
    const name = 'Test User';
    const agree = true;
    
    it('should create account successfully', async () => {
      // Mock successful response
      mock.onPost(`${API_BASE_URL}/m/oauth2/com/create_account`).reply(200, {
        id: 123,
        profile_name: name,
        email: email
      } as UserResponse);

      // Make the API call
      const response = await axios.post<UserResponse>(
        `${API_BASE_URL}/m/oauth2/com/create_account`,
        { name, email, password, agree },
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      // Verify the response
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('id');
      expect(response.data).toHaveProperty('profile_name');
      expect(response.data.profile_name).toBe(name);
    });
  });
  
  // Revoke Token test
  describe('Revoke Token API', () => {
    const refreshToken = 'c3d7f6f4b7cc640214ae0cba2b194872c3089f1c';
    
    it('should revoke token successfully', async () => {
      // Mock successful response
      mock.onPost(`${API_BASE_URL}/m/oauth2/revoke`).reply(200, {
        code: 200
      } as StatusResponse);

      // Make the API call
      const response = await axios.post<StatusResponse>(
        `${API_BASE_URL}/m/oauth2/revoke`,
        { token: refreshToken, client_id: clientId }
      );

      // Verify the response
      expect(response.status).toBe(200);
      expect(response.data).toEqual({ code: 200 });
    });
  });
});
