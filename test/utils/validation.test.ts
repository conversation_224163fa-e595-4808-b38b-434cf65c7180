import { isValidEmail, isValidPassword, sanitizeInput, validateIPAddress } from '../../utils/validation';

describe('Validation Utilities', () => {
  describe('Email Validation', () => {
    it('should validate correct email formats', () => {
      // Arrange
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];
      
      // Act & Assert
      validEmails.forEach(email => {
        expect(isValidEmail(email)).toBe(true);
      });
    });

    it('should support international domain names', () => {
      // Arrange
      const internationalEmail = 'user@例子.测试';  // Chinese domain
      
      // Act & Assert
      expect(isValidEmail(internationalEmail)).toBe(true);
    });
    
    it('should reject email with empty local part', () => {
      // Arrange
      const invalidEmail = '@example.com';
      
      // Act & Assert
      expect(isValidEmail(invalidEmail)).toBe(false);
    });
    
    it('should reject email with local part exceeding 64 characters', () => {
      // Arrange
      const tooLongLocalPart = 'a'.repeat(65);
      const invalidEmail = `${tooLongLocalPart}@example.com`;
      
      // Act & Assert
      expect(isValidEmail(invalidEmail)).toBe(false);
    });
    
    it('should reject email with domain exceeding 255 characters', () => {
      // Arrange
      const tooLongDomain = 'a'.repeat(250) + '.com';
      const invalidEmail = `test@${tooLongDomain}`;
      
      // Act & Assert
      expect(isValidEmail(invalidEmail)).toBe(false);
    });
    
    it('should reject email with invalid characters in local part', () => {
      // Arrange
      const invalidEmail = 'test <EMAIL>'; // Space not allowed in local part
      
      // Act & Assert
      expect(isValidEmail(invalidEmail)).toBe(false);
    });
    
    it('should reject email with no TLD', () => {
      // Arrange
      const invalidEmail = 'test@example';
      
      // Act & Assert
      expect(isValidEmail(invalidEmail)).toBe(false);
    });
    
    it('should reject null or undefined inputs', () => {
      // Act & Assert
      expect(isValidEmail(null as any)).toBe(false);
      expect(isValidEmail(undefined as any)).toBe(false);
    });
    
    it('should reject non-string inputs', () => {
      // Act & Assert
      expect(isValidEmail(123 as any)).toBe(false);
      expect(isValidEmail({} as any)).toBe(false);
    });
  });

  describe('Password Validation', () => {
    it('should validate strong passwords', () => {
      // Arrange
      const validPasswords = [
        'Password123!',
        'Secure@456Password',
        'A1b2C3d4!',
        'P@ssw0rd_2023',
      ];
      
      // Act & Assert
      validPasswords.forEach(password => {
        expect(isValidPassword(password)).toBe(true);
      });
    });
    
    it('should reject passwords shorter than 8 characters', () => {
      // Arrange
      const shortPassword = 'Pw1!';
      
      // Act & Assert
      expect(isValidPassword(shortPassword)).toBe(false);
    });
    
    it('should reject passwords without uppercase letters', () => {
      // Arrange
      const noUppercase = 'password123!';
      
      // Act & Assert
      expect(isValidPassword(noUppercase)).toBe(false);
    });
    
    it('should reject passwords without lowercase letters', () => {
      // Arrange
      const noLowercase = 'PASSWORD123!';
      
      // Act & Assert
      expect(isValidPassword(noLowercase)).toBe(false);
    });
    
    it('should reject passwords without numbers', () => {
      // Arrange
      const noNumbers = 'Password!';
      
      // Act & Assert
      expect(isValidPassword(noNumbers)).toBe(false);
    });
    
    it('should reject passwords without special characters', () => {
      // Arrange
      const noSpecial = 'Password123';
      
      // Act & Assert
      expect(isValidPassword(noSpecial)).toBe(false);
    });
    
    it('should reject null or undefined inputs', () => {
      // Act & Assert
      expect(isValidPassword(null as any)).toBe(false);
      expect(isValidPassword(undefined as any)).toBe(false);
    });
  });

  describe('Input Sanitization', () => {
    it('should remove script tags from input', () => {
      // Arrange
      const unsafeInput = '<script>alert("XSS")</script>Hello World';
      
      // Act
      const result = sanitizeInput(unsafeInput);
      
      // Assert
      expect(result).toBe('Hello World');
      expect(result).not.toContain('<script>');
    });
    
    it('should handle nested and complex script tags', () => {
      // Arrange
      const complexInput = 'Test <script type="text/javascript">/* <![CDATA[ */ alert("XSS"); /* ]]> */</script> Content';
      
      // Act
      const result = sanitizeInput(complexInput);
      
      // Assert
      expect(result).toBe('Test  Content');
    });
    
    it('should remove event handlers from HTML', () => {
      // Arrange
      const unsafeInput = '<div onclick="alert(\'XSS\')">Click me</div>';
      
      // Act
      const result = sanitizeInput(unsafeInput);
      
      // Assert
      expect(result).not.toContain('onclick=');
      expect(result).toContain('<div>Click me</div>');
    });
    
    it('should remove javascript: protocol from URLs', () => {
      // Arrange
      const unsafeInput = '<a href="javascript:alert(\'XSS\')">Click me</a>';
      
      // Act
      const result = sanitizeInput(unsafeInput);
      
      // Assert
      expect(result).not.toContain('javascript:');
      expect(result).toContain('<a href="alert(\'XSS\')">Click me</a>');
    });
    
    it('should handle mixed case script tags', () => {
      // Arrange
      const mixedCase = '<ScRiPt>alert("XSS")</sCrIpT>';
      
      // Act
      const result = sanitizeInput(mixedCase);
      
      // Assert
      expect(result).toBe('');
    });
  });

  describe('IP Address Validation', () => {
    it('should validate correct IPv4 addresses', () => {
      // Arrange
      const validIPs = [
        '***********',
        '********',
        '**********',
        '127.0.0.1'
      ];
      
      // Act & Assert
      validIPs.forEach(ip => {
        expect(validateIPAddress(ip)).toBe(true);
      });
    });
    
    it('should validate IPv4 address at minimum values', () => {
      // Arrange
      const minIP = '0.0.0.0';
      
      // Act & Assert
      expect(validateIPAddress(minIP)).toBe(true);
    });
    
    it('should validate IPv4 address at maximum values', () => {
      // Arrange
      const maxIP = '***************';
      
      // Act & Assert
      expect(validateIPAddress(maxIP)).toBe(true);
    });
    
    it('should reject invalid IPv4 addresses', () => {
      // Arrange
      const invalidIPs = [
        '256.0.0.1',    // Octet value > 255
        '192.168.1',    // Missing octet
        '***********.5', // Too many octets
        '************',  // Leading zeros
        '192.168.1.a',   // Non-numeric character
        '192.168..1'     // Empty octet
      ];
      
      // Act & Assert
      invalidIPs.forEach(ip => {
        expect(validateIPAddress(ip)).toBe(false);
      });
    });
  });
});
