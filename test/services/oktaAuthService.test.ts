// Unit tests for AuthService with Okta integration

import { AuthService } from '../../services/authService';
import { OktaAuthRepository } from '../../repositories/oktaAuthRepository';

// Mock the OktaAuthRepository
jest.mock('../../repositories/oktaAuthRepository');

describe('AuthService with Ok<PERSON>', () => {
  let authService: AuthService;
  let mockOktaAuthRepository: jest.Mocked<OktaAuthRepository>;
  
  beforeEach(() => {
    mockOktaAuthRepository = new OktaAuthRepository() as jest.Mocked<OktaAuthRepository>;
    authService = new AuthService(mockOktaAuthRepository);
    jest.clearAllMocks();
  });
  
  describe('Initialization', () => {
    it('should initialize Okta configuration', async () => {
      // Arrange
      const oktaConfig = {
        clientId: 'test-client-id',
        redirectUri: 'com.example.app:/callback',
        endSessionRedirectUri: 'com.example.app:/logout',
        discoveryUri: 'https://example.okta.com/oauth2/default'
      };
      
      mockOktaAuthRepository.configure.mockResolvedValue(true);
      
      // Act
      const result = await authService.initializeOkta(oktaConfig);
      
      // Assert
      expect(mockOktaAuthRepository.configure).toHaveBeenCalledWith(oktaConfig);
      expect(result).toBe(true);
    });
  });
  
  describe('Authentication', () => {
    it('should login successfully via Okta', async () => {
      // Arrange
      mockOktaAuthRepository.login.mockResolvedValue({ success: true });
      mockOktaAuthRepository.getAccessToken.mockReturnValue('okta-access-token');
      mockOktaAuthRepository.getIdToken.mockReturnValue('okta-id-token');
      
      // Act
      const result = await authService.login();
      
      // Assert
      expect(mockOktaAuthRepository.login).toHaveBeenCalled();
      expect(result).toEqual({
        success: true,
        accessToken: 'okta-access-token',
        idToken: 'okta-id-token'
      });
    });
    
    it('should handle login failure', async () => {
      // Arrange
      mockOktaAuthRepository.login.mockResolvedValue({ 
        success: false, 
        error: 'Authentication failed' 
      });
      
      // Act & Assert
      await expect(authService.login()).rejects.toThrow('Authentication failed');
    });
    
    it('should handle network errors during login', async () => {
      // Arrange
      const networkError = new Error('Network Error');
      networkError.name = 'NetworkError';
      mockOktaAuthRepository.login.mockRejectedValue(networkError);
      
      // Act & Assert
      await expect(authService.login()).rejects.toThrow('Authentication service is temporarily unavailable');
    });
  });
  
  describe('Token Verification', () => {
    it('should verify token successfully', async () => {
      // Arrange
      mockOktaAuthRepository.isAuthenticated.mockResolvedValue(true);
      mockOktaAuthRepository.getAccessToken.mockReturnValue('okta-access-token');
      
      // Act
      const result = await authService.verifyToken('okta-access-token');
      
      // Assert
      expect(mockOktaAuthRepository.isAuthenticated).toHaveBeenCalled();
      expect(result).toBe(true);
    });
    
    it('should reject invalid token', async () => {
      // Arrange
      mockOktaAuthRepository.isAuthenticated.mockResolvedValue(true);
      mockOktaAuthRepository.getAccessToken.mockReturnValue('okta-access-token');
      
      // Act
      const result = await authService.verifyToken('invalid-token');
      
      // Assert
      expect(result).toBe(false);
    });
    
    it('should reject when not authenticated', async () => {
      // Arrange
      mockOktaAuthRepository.isAuthenticated.mockResolvedValue(false);
      
      // Act
      const result = await authService.verifyToken('any-token');
      
      // Assert
      expect(result).toBe(false);
    });
  });
  
  describe('Token Refresh', () => {
    it('should refresh tokens successfully', async () => {
      // Arrange
      mockOktaAuthRepository.refreshTokens.mockResolvedValue(true);
      mockOktaAuthRepository.getAccessToken.mockReturnValue('new-access-token');
      mockOktaAuthRepository.getIdToken.mockReturnValue('new-id-token');
      
      // Act
      const result = await authService.refreshToken();
      
      // Assert
      expect(mockOktaAuthRepository.refreshTokens).toHaveBeenCalled();
      expect(result).toEqual({
        success: true,
        accessToken: 'new-access-token',
        idToken: 'new-id-token'
      });
    });
    
    it('should handle refresh failure', async () => {
      // Arrange
      mockOktaAuthRepository.refreshTokens.mockResolvedValue(false);
      
      // Act & Assert
      await expect(authService.refreshToken()).rejects.toThrow('Failed to refresh tokens');
    });
    
    it('should handle errors during refresh', async () => {
      // Arrange
      mockOktaAuthRepository.refreshTokens.mockRejectedValue(new Error('Token expired'));
      
      // Act & Assert
      await expect(authService.refreshToken()).rejects.toThrow('Session expired, please login again');
    });
  });
  
  describe('Authorization', () => {
    it('should check user roles correctly', async () => {
      // Arrange
      const userId = 'user-123';
      mockOktaAuthRepository.getUserRoles.mockResolvedValue(['admin', 'user']);
      
      // Act
      const hasAdminRole = await authService.hasRole(userId, 'admin');
      const hasModeratorRole = await authService.hasRole(userId, 'moderator');
      
      // Assert
      expect(mockOktaAuthRepository.getUserRoles).toHaveBeenCalledWith(userId);
      expect(hasAdminRole).toBe(true);
      expect(hasModeratorRole).toBe(false);
    });
    
    it('should check user permissions correctly', async () => {
      // Arrange
      const userId = 'user-123';
      mockOktaAuthRepository.getUserPermissions.mockResolvedValue([
        'read:data', 'write:data', 'delete:data'
      ]);
      
      // Act
      const hasReadPermission = await authService.hasPermission(userId, ['read:data']);
      const hasManagePermission = await authService.hasPermission(userId, ['manage:data']);
      
      // Assert
      expect(mockOktaAuthRepository.getUserPermissions).toHaveBeenCalledWith(userId);
      expect(hasReadPermission).toBe(true);
      expect(hasManagePermission).toBe(false);
    });
  });
  
  describe('Logout', () => {
    it('should logout successfully', async () => {
      // Arrange
      mockOktaAuthRepository.getAccessToken.mockReturnValue('access-token');
      mockOktaAuthRepository.revokeAccessToken.mockResolvedValue(true);
      
      // Act
      const result = await authService.logout();
      
      // Assert
      expect(mockOktaAuthRepository.revokeAccessToken).toHaveBeenCalled();
      expect(result).toEqual({ success: true });
    });
    
    it('should handle errors during logout', async () => {
      // Arrange
      mockOktaAuthRepository.getAccessToken.mockReturnValue('access-token');
      mockOktaAuthRepository.revokeAccessToken.mockRejectedValue(new Error('Revocation failed'));
      
      // Act & Assert
      await expect(authService.logout()).rejects.toThrow('Logout failed');
    });
  });
});
