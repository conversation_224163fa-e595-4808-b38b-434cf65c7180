// Unit tests for AuthService

import { AuthService } from '../../services/authService';
import { AuthRepository } from '../../repositories/authRepository';

// Mock the AuthRepository
// Note: For jest.mock specifically, we need to use relative paths even though imports use path aliases
jest.mock('../../repositories/authRepository');

describe('AuthService', () => {
  let authService: AuthService;
  let mockAuthRepository: jest.Mocked<AuthRepository>;
  
  beforeEach(() => {
    mockAuthRepository = new AuthRepository() as jest.Mocked<AuthRepository>;
    authService = new AuthService(mockAuthRepository);
    jest.clearAllMocks();
  });
  
  describe('User Registration', () => {
    it('should register a user with valid data', async () => {
      // Arrange
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Password123!'
      };
      
      mockAuthRepository.createAccount.mockResolvedValue({ success: true, user_id: '123' });
      
      // Act
      const result = await authService.registerUser(userData);
      
      // Assert
      expect(mockAuthRepository.createAccount).toHaveBeenCalledWith(userData);
      expect(result).toEqual({ success: true, userId: '123' });
    });

    it('should reject registration with invalid email format', async () => {
      // Arrange
      const userData = {
        name: 'Test User',
        email: 'invalid-email',
        password: 'Password123!'
      };
      
      // Act & Assert
      const result = await authService.registerUser(userData);
      expect(result).toEqual({ success: false, message: 'Invalid email format' });
      expect(mockAuthRepository.createAccount).not.toHaveBeenCalled();
    });

    it('should reject registration with weak password', async () => {
      // Arrange
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password' // Common weak password
      };
      
      // Act & Assert
      await expect(authService.registerUser(userData)).rejects.toThrow('Password does not meet strength requirements');
      expect(mockAuthRepository.createAccount).not.toHaveBeenCalled();
    });

    it('should reject registration with existing email', async () => {
      // Arrange
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Password123!'
      };
      
      // Mock repository to throw error when email exists
      const error = new Error('Email already exists');
      mockAuthRepository.createAccount.mockRejectedValue(error);
      
      // Act & Assert
      await expect(authService.registerUser(userData)).rejects.toThrow('Email already exists');
      expect(mockAuthRepository.createAccount).toHaveBeenCalledWith(userData);
    });
  });
  
  describe('Authentication', () => {
    
    it('should login successfully with valid credentials', async () => {
      // Arrange
      const credentials = {
        email: '<EMAIL>',
        password: 'Password123!',
      };
      
      const mockTokenResponse = {
        access_token: 'mock_access_token',
        refresh_token: 'mock_refresh_token',
        expires_in: 3600,
        token_type: 'Bearer'
      };
      
      mockAuthRepository.getToken.mockResolvedValue(mockTokenResponse);
      
      // Act
      const result = await authService.login(credentials.email, credentials.password);
      
      // Assert
      expect(mockAuthRepository.getToken).toHaveBeenCalledWith({
        grant_type: 'password',
        username: credentials.email,
        password: credentials.password,
        client_id: expect.any(String)
      });
      expect(result).toEqual({
        success: true,
        accessToken: mockTokenResponse.access_token,
        refreshToken: mockTokenResponse.refresh_token
      });
    });

    it('should reject login with incorrect password', async () => {
      // Arrange
      const credentials = {
        email: '<EMAIL>',
        password: 'WrongPassword123!',
      };
      
      // Mock repository to throw error for invalid credentials
      const error = new Error('Invalid credentials');
      mockAuthRepository.getToken.mockRejectedValue(error);
      
      // Act & Assert
      await expect(authService.login(credentials.email, credentials.password)).rejects.toThrow('Invalid credentials');
      expect(mockAuthRepository.getToken).toHaveBeenCalledWith({
        grant_type: 'password',
        username: credentials.email,
        password: credentials.password,
        client_id: expect.any(String)
      });
    });

    it('should reject login with non-existent user', async () => {
      // Arrange
      const credentials = {
        email: '<EMAIL>',
        password: 'Password123!',
      };
      
      // Mock repository to throw error for non-existent user
      const error = new Error('User not found');
      mockAuthRepository.getToken.mockRejectedValue(error);
      
      // Act & Assert
      await expect(authService.login(credentials.email, credentials.password)).rejects.toThrow('User not found');
      expect(mockAuthRepository.getToken).toHaveBeenCalledWith({
        grant_type: 'password',
        username: credentials.email,
        password: credentials.password,
        client_id: expect.any(String)
      });
    });

    it('should verify token correctly', async () => {
      // Arrange
      const token = 'mock_access_token';
      mockAuthRepository.getAccessToken.mockReturnValue(token);
      
      // Act
      const result = await authService.verifyToken(token);
      
      // Assert
      expect(result).toBe(true);
    });
  });
  
  describe('Session Management', () => {
    it('should validate unexpired access token', async () => {
      // Arrange
      const validToken = 'valid_access_token';
      mockAuthRepository.getAccessToken.mockReturnValue(validToken);
      
      // Act
      const result = await authService.validateToken(validToken);
      
      // Assert
      expect(result).toBe(true);
    });

    it('should reject expired token', async () => {
      // Arrange
      const expiredToken = 'expired_token';
      
      // Mock the validateToken method to simulate expired token
      // In a real implementation, the service would decode the token and check its expiration
      mockAuthRepository.getAccessToken.mockReturnValue(null);
      
      // Act & Assert
      await expect(authService.validateToken(expiredToken)).rejects.toThrow('Token expired');
    });

    it('should refresh token when expired', async () => {
      // Arrange
      const expiredToken = 'expired_token';
      const refreshToken = 'refresh_token';
      const newAccessToken = 'new_access_token';
      
      mockAuthRepository.getRefreshToken.mockReturnValue(refreshToken);
      mockAuthRepository.getToken.mockResolvedValue({
        access_token: newAccessToken,
        refresh_token: refreshToken,
        expires_in: 3600,
        token_type: 'Bearer'
      });
      
      // Act
      const result = await authService.refreshToken();
      
      // Assert
      expect(mockAuthRepository.getToken).toHaveBeenCalledWith({
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
        client_id: expect.any(String)
      });
      expect(result).toEqual({
        success: true,
        accessToken: newAccessToken,
        refreshToken: refreshToken
      });
    });
  });
  
  describe('Password Management', () => {
    it('should send password reset request successfully', async () => {
      // Arrange
      const email = '<EMAIL>';
      mockAuthRepository.resetPasswordSendRequest.mockResolvedValue({
        success: true,
        message: 'Reset code sent'
      });
      
      // Act
      const result = await authService.requestPasswordReset(email);
      
      // Assert
      expect(mockAuthRepository.resetPasswordSendRequest).toHaveBeenCalledWith(email);
      expect(result).toEqual({
        success: true,
        message: 'Reset code sent'
      });
    });

    it('should validate password reset code successfully', async () => {
      // Arrange
      const email = '<EMAIL>';
      const code = '123456';
      mockAuthRepository.resetPasswordCheckCode.mockResolvedValue({
        success: true,
        valid: true
      });
      
      // Act
      const result = await authService.validateResetCode(email, code);
      
      // Assert
      expect(mockAuthRepository.resetPasswordCheckCode).toHaveBeenCalledWith(email, code);
      expect(result).toEqual({
        success: true,
        valid: true
      });
    });
    
    it('should reject password reset code validation with invalid code', async () => {
      // Arrange
      const email = '<EMAIL>';
      const code = '123456';
      mockAuthRepository.resetPasswordCheckCode.mockResolvedValue({
        success: true,
        valid: false
      });
      
      // Act & Assert
      await expect(authService.validateResetCode(email, code)).rejects.toThrow('Invalid reset code');
      expect(mockAuthRepository.resetPasswordCheckCode).toHaveBeenCalledWith(email, code);
    });

    it('should complete password reset successfully with valid code and new password', async () => {
      // Arrange
      const email = '<EMAIL>';
      const code = '123456';
      const newPassword = 'NewPassword123!';
      
      mockAuthRepository.resetPasswordCheckCode.mockResolvedValue({
        success: true,
        valid: true
      });
      
      mockAuthRepository.resetPassword.mockResolvedValue({
        success: true,
        message: 'Password reset successful'
      });
      
      // Act
      const result = await authService.completePasswordReset(email, code, newPassword);
      
      // Assert
      expect(mockAuthRepository.resetPasswordCheckCode).toHaveBeenCalledWith(email, code);
      expect(mockAuthRepository.resetPassword).toHaveBeenCalledWith(email, code, newPassword);
      expect(result).toEqual({
        success: true,
        message: 'Password reset successful'
      });
    });

    it('should reject password reset with invalid code', async () => {
      // Arrange
      const email = '<EMAIL>';
      const code = '123456';
      const newPassword = 'NewPassword123!';
      
      mockAuthRepository.resetPasswordCheckCode.mockResolvedValue({
        success: true,
        valid: false
      });
      
      // Act & Assert
      await expect(authService.completePasswordReset(email, code, newPassword)).rejects.toThrow('Invalid reset code');
      expect(mockAuthRepository.resetPasswordCheckCode).toHaveBeenCalledWith(email, code);
      expect(mockAuthRepository.resetPassword).not.toHaveBeenCalled();
    });
    
    it('should reject password reset with weak new password', async () => {
      // Arrange
      const email = '<EMAIL>';
      const code = '123456';
      const newPassword = 'password'; // Weak password
      
      mockAuthRepository.resetPasswordCheckCode.mockResolvedValue({
        success: true,
        valid: true
      });
      
      // Act & Assert
      await expect(authService.completePasswordReset(email, code, newPassword)).rejects.toThrow('Password does not meet strength requirements');
      expect(mockAuthRepository.resetPasswordCheckCode).toHaveBeenCalledWith(email, code);
      expect(mockAuthRepository.resetPassword).not.toHaveBeenCalled();
    });
    it('should change password successfully for authenticated user', async () => {
      // Arrange
      const userId = '123';
      const currentPassword = 'CurrentPassword123!';
      const newPassword = 'NewPassword123!';
      
      mockAuthRepository.changePassword.mockResolvedValue({
        success: true,
        message: 'Password changed successfully'
      });
      
      // Act
      const result = await authService.changePassword(userId, currentPassword, newPassword);
      
      // Assert
      expect(mockAuthRepository.changePassword).toHaveBeenCalledWith(userId, currentPassword, newPassword);
      expect(result).toEqual({
        success: true,
        message: 'Password changed successfully'
      });
    });
    
    it('should reject password change with incorrect current password', async () => {
      // Arrange
      const userId = '123';
      const currentPassword = 'WrongPassword123!';
      const newPassword = 'NewPassword123!';
      
      const error = new Error('Current password is incorrect');
      mockAuthRepository.changePassword.mockRejectedValue(error);
      
      // Act & Assert
      await expect(authService.changePassword(userId, currentPassword, newPassword)).rejects.toThrow('Current password is incorrect');
      expect(mockAuthRepository.changePassword).toHaveBeenCalledWith(userId, currentPassword, newPassword);
    });
    
    it('should reject password change with weak new password', async () => {
      // Arrange
      const userId = '123';
      const currentPassword = 'CurrentPassword123!';
      const newPassword = 'password'; // Weak password
      
      // Act & Assert
      await expect(authService.changePassword(userId, currentPassword, newPassword)).rejects.toThrow('Password does not meet strength requirements');
      expect(mockAuthRepository.changePassword).not.toHaveBeenCalled();
    });
  });
  
  describe('Authorization', () => {
    it('should check if user has required role', async () => {
      // Arrange
      const userId = '123';
      const role = 'admin';
      
      mockAuthRepository.getUserRoles.mockResolvedValue(['admin', 'user']);
      
      // Act
      const result = await authService.hasRole(userId, role);
      
      // Assert
      expect(mockAuthRepository.getUserRoles).toHaveBeenCalledWith(userId);
      expect(result).toBe(true);
    });
    
    it('should return false when user does not have the required role', async () => {
      // Arrange
      const userId = '123';
      const role = 'admin';
      
      mockAuthRepository.getUserRoles.mockResolvedValue(['user']);
      
      // Act
      const result = await authService.hasRole(userId, role);
      
      // Assert
      expect(mockAuthRepository.getUserRoles).toHaveBeenCalledWith(userId);
      expect(result).toBe(false);
    });
    
    it('should check if user has any of the required permissions', async () => {
      // Arrange
      const userId = '123';
      const permissions = ['read:users', 'write:users'];
      
      mockAuthRepository.getUserPermissions.mockResolvedValue(['read:users', 'read:posts']);
      
      // Act
      const result = await authService.hasPermission(userId, permissions);
      
      // Assert
      expect(mockAuthRepository.getUserPermissions).toHaveBeenCalledWith(userId);
      expect(result).toBe(true);
    });
    
    it('should return false when user does not have any of the required permissions', async () => {
      // Arrange
      const userId = '123';
      const permissions = ['write:users', 'delete:users'];
      
      mockAuthRepository.getUserPermissions.mockResolvedValue(['read:users', 'read:posts']);
      
      // Act
      const result = await authService.hasPermission(userId, permissions);
      
      // Assert
      expect(mockAuthRepository.getUserPermissions).toHaveBeenCalledWith(userId);
      expect(result).toBe(false);
    });
  });
  
  describe('Security', () => {
    it('should verify CSRF token successfully', async () => {
      // Arrange
      const csrfToken = 'valid_csrf_token';
      const sessionId = 'user_session_123';
      
      mockAuthRepository.verifyCsrfToken.mockResolvedValue(true);
      
      // Act
      const result = await authService.verifyCsrf(csrfToken, sessionId);
      
      // Assert
      expect(mockAuthRepository.verifyCsrfToken).toHaveBeenCalledWith(csrfToken, sessionId);
      expect(result).toBe(true);
    });
    
    it('should reject invalid CSRF token', async () => {
      // Arrange
      const csrfToken = 'invalid_csrf_token';
      const sessionId = 'user_session_123';
      
      mockAuthRepository.verifyCsrfToken.mockResolvedValue(false);
      
      // Act & Assert
      await expect(authService.verifyCsrf(csrfToken, sessionId)).rejects.toThrow('Invalid CSRF token');
      expect(mockAuthRepository.verifyCsrfToken).toHaveBeenCalledWith(csrfToken, sessionId);
    });

    it('should detect and block brute force login attempts', async () => {
      // Arrange
      const ip = '***********';
      const email = '<EMAIL>';
      
      mockAuthRepository.checkLoginAttempts.mockResolvedValue({
        remainingAttempts: 0,
        lockedOut: true
      });
      // Act & Assert
      await expect(authService.validateLoginAttempt(ip, email)).rejects.toThrow('Too many failed login attempts');
      expect(mockAuthRepository.checkLoginAttempts).toHaveBeenCalledWith(ip, email);
    });
    
    it('should allow login when under threshold', async () => {
      // Arrange
      const ip = '***********';
      const email = '<EMAIL>';
      
      mockAuthRepository.checkLoginAttempts.mockResolvedValue({
        remainingAttempts: 3,
        lockedOut: false
      });
      
      // Act
      const result = await authService.validateLoginAttempt(ip, email);
      
      // Assert
      expect(mockAuthRepository.checkLoginAttempts).toHaveBeenCalledWith(ip, email);
      expect(result).toEqual({
        allowed: true,
        remainingAttempts: 3
      });
    });
    
    it('should sanitize user input to prevent XSS attacks', () => {
      // Arrange
      const unsafeInput = '<script>alert("XSS")</script>';
      
      // Act
      const result = authService.sanitizeInput(unsafeInput);
      
      // Assert
      expect(result).not.toContain('<script>');
      expect(result).not.toEqual(unsafeInput);
    });
    
    it('should validate IP address format', () => {
      // Arrange
      const validIP = '***********';
      const invalidIP = '999.999.999.999';
      
      // Act & Assert
      expect(authService.validateIPAddress(validIP)).toBe(true);
      expect(authService.validateIPAddress(invalidIP)).toBe(false);
    });
  });
  
  describe('Logout', () => {
    it('should logout user successfully by revoking token', async () => {
      // Arrange
      const accessToken = 'mock_access_token';
      mockAuthRepository.getAccessToken.mockReturnValue(accessToken);
      mockAuthRepository.revokeToken.mockResolvedValue({ success: true });
      
      // Act
      const result = await authService.logout();
      
      // Assert
      expect(mockAuthRepository.revokeToken).toHaveBeenCalledWith(accessToken);
      expect(result).toEqual({ success: true });
    });

    it('should return error when no active session found', async () => {
      // Arrange
      mockAuthRepository.getAccessToken.mockReturnValue(null);
      
      // Act & Assert
      await expect(authService.logout()).rejects.toThrow('No active session found');
      expect(mockAuthRepository.revokeToken).not.toHaveBeenCalled();
    });

    it('should logout from all devices by revoking all tokens', async () => {
      // Arrange
      const userId = '123';
      const accessToken = 'mock_access_token';
      mockAuthRepository.getAccessToken.mockReturnValue(accessToken);
      mockAuthRepository.revokeAllTokens.mockResolvedValue({
        success: true,
        tokensRevoked: 3
      });
      
      // Act
      const result = await authService.logoutAllDevices(userId);
      
      // Assert
      expect(mockAuthRepository.revokeAllTokens).toHaveBeenCalledWith(userId);
      expect(result).toEqual({
        success: true,
        tokensRevoked: 3
      });
    });

    it('should clear auth cookies on logout', async () => {
      // Arrange
      const accessToken = 'mock_access_token';
      mockAuthRepository.getAccessToken.mockReturnValue(accessToken);
      mockAuthRepository.revokeToken.mockResolvedValue({ success: true });
      mockAuthRepository.clearAuthCookies.mockReturnValue(true);
      
      // Act
      const result = await authService.logout(true); // true indicates to clear cookies
      
      // Assert
      expect(mockAuthRepository.revokeToken).toHaveBeenCalledWith(accessToken);
      expect(mockAuthRepository.clearAuthCookies).toHaveBeenCalled();
      expect(result).toEqual({ success: true });
    });
    
    it('should invalidate session in database on logout', async () => {
      // Arrange
      const accessToken = 'mock_access_token';
      const sessionId = 'session_123';
      mockAuthRepository.getAccessToken.mockReturnValue(accessToken);
      mockAuthRepository.getSessionId.mockReturnValue(sessionId);
      mockAuthRepository.revokeToken.mockResolvedValue({ success: true });
      mockAuthRepository.invalidateSession.mockResolvedValue(true);
      
      // Act
      const result = await authService.logout();
      
      // Assert
      expect(mockAuthRepository.revokeToken).toHaveBeenCalledWith(accessToken);
      expect(mockAuthRepository.invalidateSession).toHaveBeenCalledWith(sessionId);
      expect(result).toEqual({ success: true });
    });
  });
  
  describe('Error Handling', () => {
    it('should handle network errors gracefully during login', async () => {
      // Arrange
      const credentials = {
        email: '<EMAIL>',
        password: 'Password123!',
      };
      
      // Mock a network error
      const networkError = new Error('Network Error');
      networkError.name = 'NetworkError';
      mockAuthRepository.getToken.mockRejectedValue(networkError);
      
      // Act & Assert
      await expect(authService.login(credentials.email, credentials.password)).rejects.toThrow('Authentication service is temporarily unavailable');
      expect(mockAuthRepository.getToken).toHaveBeenCalledWith({
        grant_type: 'password',
        username: credentials.email,
        password: credentials.password,
        client_id: expect.any(String)
      });
    });
    
    it('should handle API errors with specific status codes', async () => {
      // Arrange
      const credentials = {
        email: '<EMAIL>',
        password: 'Password123!',
      };
      
      // Mock an API error with status code
      const apiError = new Error('Forbidden');
      apiError.name = 'AxiosError';
      (apiError as any).response = { status: 403, data: { message: 'Account locked' } };
      mockAuthRepository.getToken.mockRejectedValue(apiError);
      
      // Act & Assert
      await expect(authService.login(credentials.email, credentials.password)).rejects.toThrow('Account locked');
    });
    

    it('should retry failed requests up to maximum retry count', async () => {
      // Arrange
      const email = '<EMAIL>';
      
      // Setup the mock to fail multiple times then succeed
      mockAuthRepository.resetPasswordSendRequest
        .mockRejectedValueOnce(new Error('Temporary failure'))
        .mockRejectedValueOnce(new Error('Temporary failure'))
        .mockResolvedValueOnce({ success: true, message: 'Reset code sent' });
      
      // Act
      const result = await authService.requestPasswordResetWithRetry(email, 3);
      
      // Assert
      expect(mockAuthRepository.resetPasswordSendRequest).toHaveBeenCalledTimes(3);
      expect(result).toEqual({ success: true, message: 'Reset code sent' });
    });
    
    it('should fail after exceeding maximum retry attempts', async () => {
      // Arrange
      const email = '<EMAIL>';
      
      // Setup the mock to fail all attempts
      const tempError = new Error('Temporary failure');
      mockAuthRepository.resetPasswordSendRequest.mockRejectedValue(tempError);
      
      // Act & Assert
      await expect(authService.requestPasswordResetWithRetry(email, 3)).rejects.toThrow('Temporary failure');
      expect(mockAuthRepository.resetPasswordSendRequest).toHaveBeenCalledTimes(3);
    });

    it('should log authentication errors appropriately', async () => {
      // Arrange
      const userId = '123';
      const error = new Error('Permission denied');
      const logSpy = jest.spyOn(console, 'error').mockImplementation();
      
      mockAuthRepository.getUserRoles.mockRejectedValue(error);
      
      // Act & Assert
      await expect(authService.hasRole(userId, 'admin')).rejects.toThrow('Permission denied');
      expect(logSpy).toHaveBeenCalledWith(expect.stringContaining('Authentication error'), error);
      
      // Cleanup
      logSpy.mockRestore();
    });

    it('should handle unexpected JSON formats gracefully', async () => {
      // Arrange
      const malformedResponse = { unexpected_format: true };
      mockAuthRepository.getAccessToken.mockReturnValue('token');
      mockAuthRepository.revokeToken.mockResolvedValue(malformedResponse);
      
      // Act
      const result = await authService.logout();
      
      // Assert - should normalize the response format
      expect(result).toEqual({ success: false, error: 'Unexpected response format' });
    });
    
    it('should handle different types of network errors during login', async () => {
      // Arrange
      const credentials = {
        email: '<EMAIL>',
        password: 'Password123!',
      };
      
      // Test multiple network error variants
      const networkErrorCases = [
        { name: 'Error', message: 'Network Error' },
        { name: 'Error', message: 'network timeout' },
        { name: 'Error', message: 'ECONNREFUSED' },
        { name: 'Error', message: 'ETIMEDOUT' }
      ];
      
      for (const errorCase of networkErrorCases) {
        // Mock a network error with specific message
        const networkError = new Error(errorCase.message);
        networkError.name = errorCase.name;
        mockAuthRepository.getToken.mockRejectedValueOnce(networkError);
        
        // Act & Assert
        await expect(authService.login(credentials.email, credentials.password))
          .rejects.toThrow('Authentication service is temporarily unavailable');
      }
    });
    
    it('should propagate validation errors during registration', async () => {
      // Arrange
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Password123!'
      };
      
      // Mock repository to throw a validation error
      const validationError = new Error('Custom validation error');
      validationError.message = 'validation: Custom field requirement';
      mockAuthRepository.createAccount.mockRejectedValue(validationError);
      
      // Act & Assert
      await expect(authService.registerUser(userData)).rejects.toThrow('validation: Custom field requirement');
      expect(mockAuthRepository.createAccount).toHaveBeenCalledWith(userData);
    });
    
    it('should properly format generic errors during registration', async () => {
      // Arrange
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Password123!'
      };
      
      // Mock repository to throw a generic error
      const genericError = new Error('Generic database error');
      mockAuthRepository.createAccount.mockRejectedValue(genericError);
      
      // Act
      const result = await authService.registerUser(userData);
      
      // Assert
      expect(mockAuthRepository.createAccount).toHaveBeenCalledWith(userData);
      expect(result).toEqual({
        success: false,
        message: 'Generic database error'
      });
    });
    
    it('should handle non-Error objects during error handling', async () => {
      // Arrange
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Password123!'
      };
      
      // Mock repository to throw a non-Error object
      mockAuthRepository.createAccount.mockRejectedValue('String error message');
      
      // Act
      const result = await authService.registerUser(userData);
      
      // Assert
      expect(mockAuthRepository.createAccount).toHaveBeenCalledWith(userData);
      expect(result).toEqual({
        success: false,
        message: 'An unknown error occurred'
      });
    });
    
    it('should handle errors during logout process', async () => {
      // Arrange
      const accessToken = 'mock_access_token';
      mockAuthRepository.getAccessToken.mockReturnValue(accessToken);
      
      // Mock repository to throw an error during revoke token
      const logoutError = new Error('Failed to revoke token');
      mockAuthRepository.revokeToken.mockRejectedValue(logoutError);
      
      // Act
      const result = await authService.logout();
      
      // Assert
      expect(mockAuthRepository.revokeToken).toHaveBeenCalledWith(accessToken);
      expect(result).toEqual({
        success: false,
        error: 'Failed to revoke token'
      });
    });
  });
  
  describe('Edge Cases', () => {

    it('should handle empty strings in user registration', async () => {
      // Arrange
      const userData = {
        name: '',  // Empty name
        email: '<EMAIL>',
        password: 'Password123!'
      };
      
      // Act & Assert
      await expect(authService.registerUser(userData)).rejects.toThrow('Name is required');
      expect(mockAuthRepository.createAccount).not.toHaveBeenCalled();
    });

    it('should handle extremely long inputs gracefully', async () => {
      // Arrange
      const veryLongString = 'a'.repeat(1000);
      const userData = {
        name: 'Test User',
        email: `test@${veryLongString}.com`,  // Very long email
        password: 'Password123!'
      };
      
      // Act & Assert
      await expect(authService.registerUser(userData)).rejects.toThrow('Email exceeds maximum length');
      expect(mockAuthRepository.createAccount).not.toHaveBeenCalled();
    });

    it('should handle duplicate login attempts with same credentials', async () => {
      // Arrange
      const credentials = {
        email: '<EMAIL>',
        password: 'Password123!',
      };
      
      const mockTokenResponse = {
        access_token: 'mock_access_token',
        refresh_token: 'mock_refresh_token',
        expires_in: 3600,
        token_type: 'Bearer'
      };
      
      mockAuthRepository.getToken.mockResolvedValue(mockTokenResponse);
      
      // Act - Login twice with same credentials
      await authService.login(credentials.email, credentials.password);
      const secondLoginResult = await authService.login(credentials.email, credentials.password);
      
      // Assert
      expect(mockAuthRepository.getToken).toHaveBeenCalledTimes(2);
      expect(secondLoginResult).toEqual({
        success: true,
        accessToken: mockTokenResponse.access_token,
        refreshToken: mockTokenResponse.refresh_token
      });
    });

    it('should reject login with null or undefined credentials', async () => {
      // Act & Assert - Undefined email
      await expect(authService.login(undefined as any, 'Password123!')).rejects.toThrow('Email is required');
      
      // Act & Assert - Null password
      await expect(authService.login('<EMAIL>', null as any)).rejects.toThrow('Password is required');
      
      expect(mockAuthRepository.getToken).not.toHaveBeenCalled();
    });

    it('should handle token refreshing when refresh token is expired', async () => {
      // Arrange
      mockAuthRepository.getRefreshToken.mockReturnValue('expired_refresh_token');
      
      const error = new Error('Refresh token expired');
      error.name = 'AxiosError';
      (error as any).response = { status: 401, data: { message: 'Refresh token expired' } };
      
      mockAuthRepository.getToken.mockRejectedValue(error);
      
      // Act & Assert
      await expect(authService.refreshToken()).rejects.toThrow('Session expired, please login again');
      expect(mockAuthRepository.getToken).toHaveBeenCalledWith({
        grant_type: 'refresh_token',
        refresh_token: 'expired_refresh_token',
        client_id: expect.any(String)
      });
    });

    it('should handle special characters in password correctly', async () => {
      // Arrange
      const userId = '123';
      const currentPassword = 'Current@Password!123';
      const newPassword = 'New@Password!123';
      
      // Password with special characters
      mockAuthRepository.changePassword.mockResolvedValue({
        success: true,
        message: 'Password changed successfully'
      });
      
      // Act
      const result = await authService.changePassword(userId, currentPassword, newPassword);
      
      // Assert
      expect(mockAuthRepository.changePassword).toHaveBeenCalledWith(userId, currentPassword, newPassword);
      expect(result).toEqual({
        success: true,
        message: 'Password changed successfully'
      });
    });

    it('should validate email with international characters properly', () => {
      // Arrange
      const internationalEmail = 'user@例子.测试';  // Chinese domain
      
      // Act & Assert
      expect(authService.validateEmailFormat(internationalEmail)).toBe(true);
    });
    
    it('should reject non-string email input in validateEmailFormat', () => {
      // Arrange & Act & Assert - Test with null
      expect(authService.validateEmailFormat(null as any)).toBe(false);
      
      // Test with undefined
      expect(authService.validateEmailFormat(undefined as any)).toBe(false);
      
      // Test with number
      expect(authService.validateEmailFormat(123 as any)).toBe(false);
      
      // Test with object
      expect(authService.validateEmailFormat({} as any)).toBe(false);
    });

    it('should handle concurrent token refreshes correctly', async () => {
      // Arrange
      const refreshToken = 'refresh_token';
      mockAuthRepository.getRefreshToken.mockReturnValue(refreshToken);
      
      // Setup scenario where multiple token refreshes happen simultaneously
      let refreshCount = 0;
      mockAuthRepository.getToken.mockImplementation(() => {
        refreshCount++;
        return Promise.resolve({
          access_token: `new_access_token_${refreshCount}`,
          refresh_token: refreshToken,
          expires_in: 3600,
          token_type: 'Bearer'
        });
      });
      
      // Act - Simulate concurrent refreshes
      const [result1, result2] = await Promise.all([
        authService.refreshToken(),
        authService.refreshToken()
      ]);
      
      // Assert
      expect(mockAuthRepository.getToken).toHaveBeenCalledTimes(2);
      expect(result1.accessToken).toBe('new_access_token_1');
      expect(result2.accessToken).toBe('new_access_token_2');
    });
  });
  
  describe('Parameter Validation', () => {
    it('should validate all required parameters for registerUser', async () => {
      // Test with missing name
      await expect(authService.registerUser({
        name: '',  // Empty name
        email: '<EMAIL>',
        password: 'Password123!'
      })).rejects.toThrow('Name is required');
      
      // Test with missing email
      await expect(authService.registerUser({
        name: 'Test User',
        email: '',
        password: 'Password123!'
      })).rejects.toThrow();
      
      // Test with missing password
      await expect(authService.registerUser({
        name: 'Test User',
        email: '<EMAIL>',
        password: ''
      })).rejects.toThrow();
      
      expect(mockAuthRepository.createAccount).not.toHaveBeenCalled();
    });
    
    it('should validate parameters for requestPasswordReset', async () => {
      // Test with invalid email
      const result = await authService.requestPasswordReset('invalid-email');
      
      expect(result).toEqual({
        success: false,
        message: 'Invalid email format'
      });
      expect(mockAuthRepository.resetPasswordSendRequest).not.toHaveBeenCalled();
    });
    
    it('should validate parameters for validateResetCode', async () => {
      // Test with invalid email
      const result = await authService.validateResetCode('invalid-email', '123456');
      
      expect(result).toEqual({
        success: false,
        message: 'Invalid email format'
      });
      expect(mockAuthRepository.resetPasswordCheckCode).not.toHaveBeenCalled();
    });
    
    it('should validate parameters for requestPasswordResetWithRetry', async () => {
      // Test with invalid email
      const result = await authService.requestPasswordResetWithRetry('invalid-email', 3);
      
      expect(result).toEqual({
        success: false,
        message: 'Invalid email format'
      });
      expect(mockAuthRepository.resetPasswordSendRequest).not.toHaveBeenCalled();
    });
  });
  
  describe('Concurrent Operations', () => {
    it('should handle concurrent password reset requests', async () => {
      // Arrange
      const email = '<EMAIL>';
      
      mockAuthRepository.resetPasswordSendRequest.mockResolvedValue({
        success: true,
        message: 'Reset code sent'
      });
      
      // Act - Simulate concurrent password reset requests
      const results = await Promise.all([
        authService.requestPasswordReset(email),
        authService.requestPasswordReset(email),
        authService.requestPasswordReset(email)
      ]);
      
      // Assert
      expect(mockAuthRepository.resetPasswordSendRequest).toHaveBeenCalledTimes(3);
      expect(mockAuthRepository.resetPasswordSendRequest).toHaveBeenCalledWith(email);
      
      // All requests should succeed
      results.forEach(result => {
        expect(result).toEqual({
          success: true,
          message: 'Reset code sent'
        });
      });
    });
    
    it('should handle race conditions in session validation', async () => {
      // Arrange
      const token = 'mock_access_token';
      let tokenValidationCounter = 0;
      
      // Mock to simulate a race condition - token becomes invalid after first validation
      mockAuthRepository.getAccessToken.mockImplementation(() => {
        tokenValidationCounter++;
        // First call returns valid token, subsequent calls return null (expired)
        return tokenValidationCounter === 1 ? token : null;
      });
      
      // Act - First validation succeeds
      const firstResult = await authService.validateToken(token);
      
      // Assert
      expect(firstResult).toBe(true);
      
      // Act & Assert - Second validation fails due to expired token
      await expect(authService.validateToken(token)).rejects.toThrow('Token expired');
    });
  });
});