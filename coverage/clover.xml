<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1754068429648" clover="3.2.0">
  <project timestamp="1754068429648" name="All files">
    <metrics statements="46" coveredstatements="45" conditionals="6" coveredconditionals="3" methods="11" coveredmethods="11" elements="63" coveredelements="59" complexity="0" loc="46" ncloc="46" packages="1" files="1" classes="1"/>
    <file name="auth_repository.ts" path="/Users/<USER>/dev/lcc/repositories/auth_repository.ts">
      <metrics statements="46" coveredstatements="45" conditionals="6" coveredconditionals="3" methods="11" coveredmethods="11"/>
      <line num="1" count="1" type="stmt"/>
      <line num="7" count="1" type="stmt"/>
      <line num="8" count="19" type="stmt"/>
      <line num="9" count="19" type="stmt"/>
      <line num="10" count="19" type="stmt"/>
      <line num="16" count="1" type="stmt"/>
      <line num="23" count="1" type="stmt"/>
      <line num="30" count="1" type="stmt"/>
      <line num="37" count="2" type="stmt"/>
      <line num="38" count="2" type="stmt"/>
      <line num="39" count="1" type="stmt"/>
      <line num="41" count="1" type="stmt"/>
      <line num="50" count="2" type="stmt"/>
      <line num="51" count="2" type="stmt"/>
      <line num="54" count="1" type="stmt"/>
      <line num="56" count="1" type="stmt"/>
      <line num="65" count="2" type="stmt"/>
      <line num="66" count="2" type="stmt"/>
      <line num="71" count="1" type="stmt"/>
      <line num="73" count="1" type="stmt"/>
      <line num="83" count="2" type="stmt"/>
      <line num="84" count="2" type="stmt"/>
      <line num="89" count="1" type="stmt"/>
      <line num="91" count="1" type="stmt"/>
      <line num="100" count="2" type="stmt"/>
      <line num="101" count="2" type="stmt"/>
      <line num="106" count="1" type="stmt"/>
      <line num="108" count="1" type="stmt"/>
      <line num="118" count="2" type="stmt"/>
      <line num="119" count="2" type="stmt"/>
      <line num="124" count="1" type="stmt"/>
      <line num="126" count="1" type="stmt"/>
      <line num="140" count="2" type="stmt"/>
      <line num="141" count="2" type="stmt"/>
      <line num="148" count="1" type="cond" truecount="1" falsecount="1"/>
      <line num="149" count="1" type="stmt"/>
      <line num="151" count="1" type="cond" truecount="1" falsecount="1"/>
      <line num="152" count="1" type="stmt"/>
      <line num="155" count="1" type="stmt"/>
      <line num="157" count="1" type="stmt"/>
      <line num="166" count="2" type="stmt"/>
      <line num="167" count="2" type="stmt"/>
      <line num="174" count="1" type="cond" truecount="1" falsecount="1"/>
      <line num="175" count="0" type="stmt"/>
      <line num="178" count="1" type="stmt"/>
      <line num="180" count="1" type="stmt"/>
    </file>
  </project>
</coverage>
