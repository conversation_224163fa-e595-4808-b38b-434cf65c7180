# Una Authentication Differences

## Overview
This document outlines how authentication in this Una social network project differs from a standard Una installation.

## Key Differences in Authentication

### 1. Okta Integration
The most significant difference is the integration with Okta for authentication. This project uses a custom module called "OktaConnect" that implements OAuth 2.0 authentication with <PERSON><PERSON>, rather than relying solely on <PERSON>'s native authentication system.

### 2. OAuth 2.0 Authentication Flow
- The authentication process uses OAuth 2.0 protocol to authenticate users through Okta
- Users are redirected to <PERSON><PERSON>'s login page rather than <PERSON>'s native login form
- After successful authentication on <PERSON><PERSON>, users are redirected back to Una with an authorization code
- The code is exchanged for access tokens and user information

### 3. User Profile Mapping
- User profile information is retrieved from Ok<PERSON> and mapped to <PERSON>'s user profile structure
- The module converts remote Okta fields to local Una fields using the `_convertRemoteFields()` method
- User attributes like name, email, and profile picture are pulled from Okta

### 4. Role-Based Access Control
- The system implements role-based authentication checks
- Only users with specific Okta roles (configured in `aAllowedRolesIds`) are allowed to access the system
- If a user doesn't have sufficient roles, they're denied access even if their credentials are valid

### 5. Configuration Options
- Custom configuration parameters for Okta integration:
  - Okta domain
  - Client ID and Secret
  - OAuth scopes
  - User ID field mapping
  - Options for handling duplicate emails

### 6. Single Sign-On Capability
- The implementation enables Single Sign-On (SSO) between Okta and Una
- Users who are already authenticated with Okta can seamlessly access the Una platform

### 7. Custom Event Hooks
- The module includes hooks for custom actions during authentication events:
  - `onLogin` - triggered when a user logs in
  - `onRegister` - triggered when a new user is registered
  - `onConvertRemoteFields` - allows customization of field mapping

## Technical Implementation
The implementation follows the OAuth 2.0 protocol as described in the Okta documentation:
https://developer.okta.com/docs/guides/implement-oauth-for-okta/main/

The authentication flow is handled primarily by the `BxOktaConModule` class, which manages:
- Redirecting users to Okta for authentication
- Processing the authentication callback
- Retrieving user information from Okta
- Creating or updating user profiles in Una
- Handling role-based access control

## React Native Authentication Strategy

### 1. Use Okta's React Native SDK

Okta provides an official SDK for React Native that will handle most of the authentication flow:

```
@okta/okta-react-native
```

This SDK supports the same OAuth 2.0 flow that the Una backend is using.

### 2. Authentication Flow Implementation

1. **Configure the SDK** with the same Okta credentials used in the Una backend:
   - Same Okta domain
   - Same client ID
   - Same scopes (`openid profile email`)
   - Use a mobile-specific redirect URI

2. **Implement Authentication Flow**:
   ```javascript
   import { createConfig, signIn, getAccessToken } from '@okta/okta-react-native';

   // Configure Okta
   await createConfig({
     clientId: 'YOUR_CLIENT_ID',
     redirectUri: 'com.yourapplication:/callback',
     endSessionRedirectUri: 'com.yourapplication:/logout',
     discoveryUri: 'https://YOUR_OKTA_DOMAIN/oauth2/default',
     scopes: ['openid', 'profile', 'email'],
     requireHardwareBackedKeyStore: false // For development
   });

   // Sign in
   const { resolve, reject } = await signIn();
   if (resolve) {
     // User authenticated successfully
     const accessToken = await getAccessToken();
     // Use this token for API calls
   }
   ```

3. **Handle the Redirect** by configuring the app to handle the custom URI scheme in `AndroidManifest.xml` and `Info.plist`.

### 3. API Communication Strategy

Since the Una backend is already set up with Okta:

1. **Pass the Okta token to Una**: Include the access token in API requests to Una:
   ```javascript
   fetch('https://your-una-api.com/endpoint', {
     headers: {
       'Authorization': `Bearer ${accessToken}`,
       'Content-Type': 'application/json'
     }
   })
   ```

2. **Create a Una API endpoint** that:
   - Validates the Okta token
   - Maps to an existing Una user or creates one (similar to what `BxOktaConModule.php` does)
   - Returns Una-specific session information if needed

### 4. Modifications Needed on Una Backend

Create a new API endpoint in Una that:

1. Accepts and validates Okta tokens from mobile clients
2. Reuses the existing `_convertRemoteFields()` and user mapping logic
3. Returns appropriate responses for mobile clients (JSON instead of redirects)

This could be a simplified version of the existing `actionHandle()` method in `BxOktaConModule.php` but designed for API use.

### 5. Security Considerations

1. **Use PKCE** (Proof Key for Code Exchange) - critical for mobile apps
2. **Token Storage** - securely store tokens using encrypted storage like `react-native-keychain`
3. **Token Refresh** - implement proper token refresh logic
4. **Certificate Pinning** - consider implementing for additional security

### 6. Testing Authentication Flow

1. Test with Okta's testing tools
2. Verify token validation on Una backend
3. Test user creation/mapping
4. Test role-based access control

## Using Server-Hosted Una Okta Setup from React Native

### Possible Approaches

1. **Web View Approach**: 
   - Embed a web view in your React Native app that loads the existing Una Okta login flow
   - After successful authentication, capture the session/tokens from the web view
   - Use these credentials for subsequent API calls

2. **Proxy Authentication**:
   - Create an API endpoint on your Una server that handles authentication
   - Your React Native app would send credentials to this endpoint
   - The endpoint would perform the Okta authentication and return tokens to your app

### Considerations

#### Pros:
- Reuses existing authentication code
- Ensures consistent authentication logic between web and mobile
- Less code to maintain (no duplicate authentication flows)
- Role-based access control already implemented

#### Cons:
- Web views provide a less native user experience
- Security concerns with capturing tokens from web views
- Mobile-specific authentication features may be harder to implement
- May not follow OAuth best practices for mobile applications

### Best Practices Assessment

This approach goes against some mobile authentication best practices:

1. **Native Experience**: OAuth implementations for mobile should use native UI components rather than web views when possible

2. **PKCE Support**: Mobile apps should use PKCE (Proof Key for Code Exchange), which might not be part of your server implementation

3. **Token Storage**: Mobile apps need secure token storage mechanisms that differ from web applications

4. **Redirect Handling**: Mobile apps handle OAuth redirects differently than web apps

### Compromise Solution

A better middle-ground approach would be:

1. Create a dedicated API endpoint in your Una backend that:
   - Accepts authentication requests from your mobile app
   - Leverages your existing Okta integration code
   - Returns tokens in a mobile-friendly format

2. Use Okta's React Native SDK for the frontend authentication flow, but:
   - Configure it to work with your existing Okta application settings
   - Have it communicate with your custom Una API endpoint

This way, you maintain most of your existing authentication logic while still following mobile authentication best practices.
