#!/bin/bash

# Una CMS Local Development Setup Script
# This script automates the setup of Una CMS for local development

set -e  # Exit on any error

# ANSI color codes for better readability
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function for displaying messages
print_message() {
    echo -e "${BLUE}[Una Setup]${NC} $1"
}

# Function for displaying success messages
print_success() {
    echo -e "${GREEN}[Success]${NC} $1"
}

# Function for displaying warnings
print_warning() {
    echo -e "${YELLOW}[Warning]${NC} $1"
}

# Function for displaying errors
print_error() {
    echo -e "${RED}[Error]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if running on macOS
if [[ "$(uname)" != "Darwin" ]]; then
    print_error "This script is designed for macOS."
    exit 1
fi

# Configuration variables - can be customized
UNA_DOMAIN="una.local"
UNA_DB_NAME="una_db"
UNA_DB_USER="una_user"
UNA_DB_PASS="una_password"
UNA_INSTALL_DIR="$HOME/una_cms"
UNA_PORT=8080

print_message "Una CMS Local Development Setup"
print_message "--------------------------------"
print_message "This script will set up Una CMS for local development with the following configuration:"
echo "- Installation directory: $UNA_INSTALL_DIR"
echo "- Database name: $UNA_DB_NAME"
echo "- Database user: $UNA_DB_USER"
echo "- Local URL: http://localhost:$UNA_PORT"
echo 

# Confirm before proceeding
read -p "Do you want to continue with this setup? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_message "Setup canceled."
    exit 0
fi

# Check for Homebrew
if ! command_exists brew; then
    print_message "Installing Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    print_success "Homebrew installed."
else
    print_success "Homebrew is already installed."
fi

# Update Homebrew
print_message "Updating Homebrew..."
brew update
print_success "Homebrew updated."

# Install PHP and required extensions
print_message "Installing PHP and required extensions..."
brew install php@8.1
brew link --overwrite php@8.1
print_success "PHP installed."

# Install MariaDB
print_message "Installing MariaDB..."
brew install mariadb
print_success "MariaDB installed."

# Start MariaDB service
print_message "Starting MariaDB service..."
brew services start mariadb
print_success "MariaDB service started."

# Wait for MariaDB to be ready
print_message "Waiting for MariaDB to be ready..."
max_attempts=30
attempts=0
while ! mysqladmin ping -h localhost --silent && [ $attempts -lt $max_attempts ]; do
    sleep 2
    attempts=$((attempts+1))
    echo "Waiting for MariaDB to be ready... Attempt $attempts/$max_attempts"
done

if [ $attempts -eq $max_attempts ]; then
    print_error "MariaDB did not become ready in time. Please try running the script again or start MariaDB manually."
    exit 1
fi

print_success "MariaDB is ready."

# Database setup
print_message "Database Setup"
print_message "---------------"

# Define MySQL database creation commands
MYSQL_CREATE_COMMANDS="CREATE DATABASE IF NOT EXISTS $UNA_DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; CREATE USER IF NOT EXISTS '$UNA_DB_USER'@'localhost' IDENTIFIED BY '$UNA_DB_PASS'; GRANT ALL ON $UNA_DB_NAME.* TO '$UNA_DB_USER'@'localhost'; FLUSH PRIVILEGES;"

DB_SETUP_REQUIRED=true
# Check if database exists using sudo
if sudo mysql -u root -e "SHOW DATABASES LIKE '$UNA_DB_NAME';" 2>/dev/null | grep -q "$UNA_DB_NAME"; then
    print_success "Database $UNA_DB_NAME already exists."
    DB_SETUP_REQUIRED=false
fi

if [ "$DB_SETUP_REQUIRED" = true ]; then
    print_message "Attempting to create database and user using sudo..."

    # Define MySQL database creation commands
    MYSQL_CREATE_COMMANDS="CREATE DATABASE IF NOT EXISTS $UNA_DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; CREATE USER IF NOT EXISTS '$UNA_DB_USER'@'localhost' IDENTIFIED BY '$UNA_DB_PASS'; GRANT ALL ON $UNA_DB_NAME.* TO '$UNA_DB_USER'@'localhost'; FLUSH PRIVILEGES;"

    # Try to create database and user using sudo
    if sudo mysql -u root -e "$MYSQL_CREATE_COMMANDS" 2>/dev/null; then
        print_success "Database and user created successfully using sudo!"
        DB_SETUP_REQUIRED=false
    else
        print_error "Failed to create database and user using sudo."
        echo
        
        if [ -z "$DB_ROOT_PASS" ]; then
            # Try once more without password explicitly
            if echo "$MYSQL_CREATE_COMMANDS" | mysql -u root 2>/dev/null; then
                print_success "Database and user created successfully!"
                DB_SETUP_REQUIRED=false
            fi
        else
            # Try with provided password
            if echo "$MYSQL_CREATE_COMMANDS" | mysql -u root -p"$DB_ROOT_PASS" 2>/dev/null; then
                print_success "Database and user created successfully using provided password!"
                DB_SETUP_REQUIRED=false
            fi
        fi
    fi
fi

if [ "$DB_SETUP_REQUIRED" = true ]; then
    print_warning "Database creation failed. Here are commands you can copy and paste to create the database manually:"
    echo "\n----- Copy and paste these commands -----"
    echo "sudo mysql -u root -e \"CREATE DATABASE IF NOT EXISTS $UNA_DB_NAME;\""
    echo "sudo mysql -u root -e \"CREATE USER IF NOT EXISTS '$UNA_DB_USER'@'localhost' IDENTIFIED BY '$UNA_DB_PASS';\""
    echo "sudo mysql -u root -e \"GRANT ALL ON $UNA_DB_NAME.* TO '$UNA_DB_USER'@'localhost';\""
    echo "sudo mysql -u root -e \"FLUSH PRIVILEGES;\""
    echo "----- End of commands -----\n"
    
    print_message "After running these commands, restart this script."
    print_warning "Would you like to try resetting the MariaDB root password as an alternative?"
    read -p "Reset MariaDB root password? (y/n) " -n 1 -r RESET_PASSWORD
    echo
    
    if [[ $RESET_PASSWORD =~ ^[Yy]$ ]]; then
        print_message "Resetting MariaDB root password..."
        
        # New root password
        NEW_ROOT_PASS="rootpassword"
        
        # Stop MariaDB service
        print_message "Stopping MariaDB service..."
        brew services stop mariadb
        sleep 3
        
        # Start MariaDB in safe mode with skip-grant-tables
        print_message "Starting MariaDB in safe mode..."
        # Run in background and save PID
        mysqld_safe --skip-grant-tables & 
        MYSQLD_PID=$!
        
        # Wait for MariaDB to start in safe mode
        print_message "Waiting for MariaDB to start in safe mode..."
        sleep 5
        
        # Update root password - handle both modern and legacy MariaDB versions
        print_message "Setting new root password..."
        PASSWORD_RESET_SUCCESS=false
        
        # First try the modern MariaDB way (for 10.4+)
        if mysql -u root -e "ALTER USER 'root'@'localhost' IDENTIFIED BY '$NEW_ROOT_PASS'; FLUSH PRIVILEGES;" 2>/dev/null; then
            print_success "Root password has been reset using ALTER USER!"
            PASSWORD_RESET_SUCCESS=true
        # Then try the legacy way if that fails
        elif mysql -u root -e "SET PASSWORD FOR 'root'@'localhost' = PASSWORD('$NEW_ROOT_PASS'); FLUSH PRIVILEGES;" 2>/dev/null; then
            print_success "Root password has been reset using SET PASSWORD!"
            PASSWORD_RESET_SUCCESS=true
        # Try different tables that might exist
        elif mysql -u root -e "SHOW DATABASES;" | grep -q "mysql" && \
             mysql -u root -e "SHOW TABLES IN mysql;" | grep -q "global_priv" && \
             mysql -u root -e "UPDATE mysql.global_priv SET priv=JSON_SET(priv, '$.plugin', 'mysql_native_password', '$.authentication_string', PASSWORD('$NEW_ROOT_PASS')) WHERE User='root'; FLUSH PRIVILEGES;" 2>/dev/null; then
            print_success "Root password has been reset using mysql.global_priv table!"
            PASSWORD_RESET_SUCCESS=true
        # If all above methods fail, try creating the root user if it doesn't exist
        elif mysql -u root -e "CREATE USER IF NOT EXISTS 'root'@'localhost' IDENTIFIED BY '$NEW_ROOT_PASS'; GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION; FLUSH PRIVILEGES;" 2>/dev/null; then
            print_success "Root user created with new password!"
            PASSWORD_RESET_SUCCESS=true
        else
            print_error "Could not update root password. Tables structure may be different."
            PASSWORD_RESET_SUCCESS=false
        fi
        
        # Kill the mysqld_safe process
        print_message "Stopping mysqld_safe..."
        kill $MYSQLD_PID 2>/dev/null
        sleep 5
        
        # Restart MariaDB service
        print_message "Restarting MariaDB service..."
        brew services start mariadb
        sleep 5
        
        # Only try to create database if password reset was successful
        if [ "$PASSWORD_RESET_SUCCESS" = true ]; then
            # Create database with new password
            print_message "Creating database with new root password..."
            if echo "$MYSQL_CREATE_COMMANDS" | mysql -u root -p"$NEW_ROOT_PASS" 2>/dev/null; then
                print_success "Database and user created successfully!"
                DB_SETUP_REQUIRED=false
                print_message "The MariaDB root password has been reset to: $NEW_ROOT_PASS"
                print_message "Please make note of this password for future database administration."
            else
                print_error "Failed to create database after password reset."
            fi
        else
            print_error "Could not reset the root password using automated methods."
        fi
    else
        print_message "Skipping MariaDB root password reset."
        # Ensure MariaDB service is running
        brew services start mariadb
    fi
    
    if [ "$DB_SETUP_REQUIRED" = true ]; then
        print_warning "Automated database creation failed. You'll need to manually create the database before completing Una CMS setup."
        print_message "Manual database creation instructions will be provided at the end of the script."
    fi
fi
echo

# Install unzip if not available
if ! command_exists unzip; then
    print_message "Installing unzip..."
    brew install unzip
    print_success "Unzip installed."
fi

# Create installation directory
print_message "Creating installation directory..."
mkdir -p "$UNA_INSTALL_DIR"
print_success "Installation directory created."

# Download Una CMS
print_message "Downloading Una CMS..."
cd /tmp

# Using GitHub releases instead of una.io/download/latest which seems to have issues
UNA_VERSION="14.0.0"
print_message "Downloading Una CMS version $UNA_VERSION from GitHub..."
curl -L "https://github.com/unacms/UNA/archive/refs/tags/$UNA_VERSION.zip" -o una.zip

# Verify download was successful
if [ ! -f una.zip ] || [ ! -s una.zip ]; then
    print_error "Failed to download Una CMS. Please check your internet connection and try again."
    exit 1
fi

print_success "Una CMS downloaded."

# Extract Una CMS
print_message "Extracting Una CMS..."

# Create a temporary directory for extraction
mkdir -p /tmp/una_extract

# Extract with verbose output in case of errors
unzip -q una.zip -d /tmp/una_extract || {
    print_error "Failed to extract Una CMS zip file. The file may be corrupted."
    ls -la una.zip
    file una.zip
    exit 1
}

# Check if extraction was successful and files exist
if [ ! -d "/tmp/una_extract/UNA-$UNA_VERSION" ]; then
    print_error "Una CMS extraction failed or directory structure is unexpected."
    ls -la /tmp/una_extract
    exit 1
fi

cp -r "/tmp/una_extract/UNA-$UNA_VERSION/"* "$UNA_INSTALL_DIR/"
rm -rf /tmp/una_extract
rm -f una.zip
print_success "Una CMS extracted to $UNA_INSTALL_DIR."

# Set permissions
print_message "Setting permissions..."
chmod -R 755 "$UNA_INSTALL_DIR"
mkdir -p "$UNA_INSTALL_DIR/inc" "$UNA_INSTALL_DIR/cache" "$UNA_INSTALL_DIR/cache_public" "$UNA_INSTALL_DIR/tmp" "$UNA_INSTALL_DIR/logs" "$UNA_INSTALL_DIR/storage"
chmod -R 777 "$UNA_INSTALL_DIR/inc" "$UNA_INSTALL_DIR/cache" "$UNA_INSTALL_DIR/cache_public" "$UNA_INSTALL_DIR/tmp" "$UNA_INSTALL_DIR/logs" "$UNA_INSTALL_DIR/storage"

# Make ffmpeg.exe executable
if [ -f "$UNA_INSTALL_DIR/plugins/ffmpeg/ffmpeg.exe" ]; then
    print_message "Making ffmpeg.exe executable..."
    chmod +x "$UNA_INSTALL_DIR/plugins/ffmpeg/ffmpeg.exe"
fi
print_success "Permissions set."

# Install Composer if not available
if ! command_exists composer; then
    print_message "Installing Composer..."
    brew install composer
    print_success "Composer installed."
fi

# Install dependencies using Composer
print_message "Installing Composer dependencies..."
cd "$UNA_INSTALL_DIR"
composer install
print_success "Composer dependencies installed."

# Add hosts entry
print_message "Adding entry to hosts file..."
echo "127.0.0.1 $UNA_DOMAIN" | sudo tee -a /etc/hosts > /dev/null
print_success "Hosts entry added."

# Create MariaDB configuration file to set default storage engine
print_message "Creating MariaDB configuration..."
CONF_FILE="/usr/local/etc/my.cnf.d/una_config.cnf"
sudo mkdir -p /usr/local/etc/my.cnf.d
cat > /tmp/una_config.cnf << EOF
[mysqld]
default-storage-engine=InnoDB
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci

[client]
default-character-set=utf8mb4
EOF
sudo mv /tmp/una_config.cnf "$CONF_FILE"

# Restart MariaDB to apply new configuration
print_message "Restarting MariaDB with new configuration..."
brew services restart mariadb
sleep 5
print_success "MariaDB configuration updated."

# Verify plugin directories exist
print_message "Verifying plugin directories..."
mkdir -p "$UNA_INSTALL_DIR/plugins_public/tailwind/css"
mkdir -p "$UNA_INSTALL_DIR/plugins_public/jquery"
mkdir -p "$UNA_INSTALL_DIR/plugins_public/jquery-ui"

# Create placeholder files to prevent 404 errors
print_message "Creating placeholder plugin files..."
touch "$UNA_INSTALL_DIR/plugins_public/tailwind/css/tailwind.min.css"
touch "$UNA_INSTALL_DIR/plugins_public/jquery/jquery.min.js"
touch "$UNA_INSTALL_DIR/plugins_public/jquery-ui/jquery-ui.min.js"
print_success "Plugin directories and placeholder files created."

# Create a PHP server startup script
print_message "Creating PHP server startup script..."
cat > "$UNA_INSTALL_DIR/start_server.sh" << EOF
#!/bin/bash
cd "$UNA_INSTALL_DIR"
echo "Starting Una CMS server on http://localhost:$UNA_PORT"
echo "Press Ctrl+C to stop the server"
php -S localhost:$UNA_PORT
EOF
chmod +x "$UNA_INSTALL_DIR/start_server.sh"
print_success "Server startup script created."

# Create a cron setup script
print_message "Creating cron setup script..."
cat > "$UNA_INSTALL_DIR/setup_cron.sh" << EOF
#!/bin/bash
# Add this to your crontab after completing the web installation
# Replace YOUR_DOMAIN with your actual domain name
echo "* * * * * curl -s http://localhost:$UNA_PORT/periodic/ >/dev/null 2>&1"
EOF
chmod +x "$UNA_INSTALL_DIR/setup_cron.sh"
print_success "Cron setup script created."

# Final instructions
print_message "Una CMS setup completed!"
print_message ""

# Manual database setup instructions
print_message "IMPORTANT: Manual Database Setup Required"
print_message "-------------------------------------"
print_message "You need to create the database and user before continuing with Una CMS installation."
print_message "Here are detailed steps to set up your MariaDB database:"
print_message ""
print_message "Option 1: Using MySQL CLI with root password (if you know it):"
echo "  1. mysql -u root -p"
echo "  2. CREATE DATABASE $UNA_DB_NAME;"
echo "  3. CREATE USER '$UNA_DB_USER'@'localhost' IDENTIFIED BY '$UNA_DB_PASS';"
echo "  4. GRANT ALL ON $UNA_DB_NAME.* TO '$UNA_DB_USER'@'localhost';"
echo "  5. FLUSH PRIVILEGES;"
echo "  6. EXIT;"
print_message ""
print_message "Option 2: Reset MariaDB root password (if you don't know it):"
echo "  1. brew services stop mariadb"
echo "  2. mysqld_safe --skip-grant-tables &"
echo "  3. mysql -u root"
echo "  4. UPDATE mysql.user SET authentication_string=PASSWORD('new_password') WHERE User='root';"
echo "  5. FLUSH PRIVILEGES;"
echo "  6. EXIT;"
echo "  crontab -e"
print_message "And adding the line from $UNA_INSTALL_DIR/setup_cron.sh"

# Final success message with copy-pasteable commands
print_success "Una CMS setup is now complete!"

print_message "To verify database connectivity, run this command:"
echo ""
echo "----- Database verification command -----"
echo "mysql -u $UNA_DB_USER -p'$UNA_DB_PASS' -e \"SHOW DATABASES;\""
echo "----- End of command -----"
echo ""

print_message "Next steps (copy and paste these commands as needed):"
echo ""
echo "----- Start the PHP server -----"
echo "cd $UNA_INSTALL_DIR && ./start_server.sh"
echo "----- End of command -----"
echo ""
echo "----- Access Una CMS -----"
echo "open http://localhost:$UNA_PORT"
echo "----- End of command -----"
echo ""
echo "----- After installation, setup cron -----"
echo "$UNA_INSTALL_DIR/setup_cron.sh"
echo "----- End of command -----"
echo ""

print_message "During web setup, use these database credentials:"
echo "  Database Host: localhost"
echo "  Database Name: $UNA_DB_NAME"
echo "  Database User: $UNA_DB_USER"
echo "  Database Password: $UNA_DB_PASS"
echo ""

print_message "Recommended values for the web setup form:"
echo "=== Site Paths Section ==="
echo "  Site URL: http://localhost:$UNA_PORT or http://una.local:$UNA_PORT"
echo "  Site Path: $UNA_INSTALL_DIR"
echo ""
echo "=== DB Config Section ==="
echo "  Database Host: localhost"
echo "  Database Host Port Number: (leave empty - default)"
echo "  Database Socket Path: (leave empty - default)"
echo "  Database Name: $UNA_DB_NAME"
echo "  Database User: $UNA_DB_USER"
echo "  Database Password: $UNA_DB_PASS"
echo "  Database Engine: (leave empty for default - InnoDB)"
echo ""
echo "=== Site Info Section ==="
echo "  Site Name: My Una Site"
echo "  Site Email: <EMAIL>"
echo "  Admin Username: admin"
echo "  Admin Password: (create a strong password)"
echo "  Admin Email: <EMAIL>"
echo ""
echo "=== Link with UNA Market Section ==="
echo "  UNA Key: (optional - can leave empty for now)"
echo "  UNA Secret: (optional - can leave empty for now)"
echo ""
echo "=== Modules Section ==="
echo "  Language: English"
echo "  Template: Artificer"
echo "  Profiles: Accounts Manager"
